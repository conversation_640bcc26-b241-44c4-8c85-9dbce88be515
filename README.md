# JOCOM Project

This is a monorepo containing multiple applications and shared packages built with Next.js and managed with Turborepo.

## Project Structure

- `apps/` - Contains all applications
  - `admin/` - Admin dashboard application
  - `frontend/` - Customer-facing frontend application
- `packages/` - Contains shared packages
  - `config/` - Shared configuration (Tai<PERSON><PERSON>, etc.)
  - `types/` - Shared TypeScript types
  - `ui/` - Shared UI components

## Prerequisites

- Node.js 18+ (recommended: use [nvm](https://github.com/nvm-sh/nvm) to manage Node versions)
- npm, yarn, or pnpm (this guide uses npm)

## Getting Started

### 1. Install Dependencies

Clone the repository and install all dependencies:

```bash
git clone https://github.com/your-org/jocom.git
cd jocom
npm install
```

### 2. Development Environment

#### Important: Build Shared Packages First

Before running any applications, you need to build the shared packages:

```bash
# Build all shared packages
npm run dev:types
npm run dev:config
npm run dev:ui
```

#### Run Applications

After building the shared packages, you can run the applications:

```bash
# Run admin application
npm run dev:admin

# Run frontend application
npm run dev:frontend

# Run all applications and packages in parallel
npm run dev
```

The applications will be available at:

- Admin: <http://localhost:3000>
- Frontend: <http://localhost:3001> (port may vary depending on configuration)

### 3. Building for Production

To create production builds:

```bash
# Build all applications and packages
npm run build

# Build specific applications
npm run build:admin
npm run build:frontend
```

### 4. Running Production Builds

To run the production builds:

```bash
# From the root directory
cd apps/admin
npm run start:prod

# Or for the frontend
cd apps/frontend
npm run start:prod
```

## Linting

Run linting across the entire monorepo:

```bash
npm run lint
```

## Additional Commands

- `npm run analyze` - Analyze bundle sizes (in specific app directories)

## Troubleshooting

If you encounter issues with dependencies between packages:

1. Make sure you've built the shared packages first
2. Try cleaning the node_modules and rebuilding:

   ```bash
   rm -rf node_modules
   rm -rf **/node_modules
   npm install
   ```

3. Check that package versions are compatible in all package.json files

## Contributing

Please see our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.
