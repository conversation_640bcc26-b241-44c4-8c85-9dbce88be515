# JOCOM Admin Dashboard

This is the admin dashboard application for the JOCOM project, built with Next.js.

## Prerequisites

- Node.js 18+ (recommended: use [nvm](https://github.com/nvm-sh/nvm) to manage Node versions)
- npm, yarn, or pnpm (this guide uses npm)

## Getting Started

### 1. Install Dependencies

From the root of the monorepo:

```bash
npm install
```

### 2. Development Environment

#### Important: Build Shared Packages First

Before running the admin application, you need to build the shared packages:

```bash
# From the root directory
npm run dev:types
npm run dev:config
npm run dev:ui
```

#### Run Admin Application

After building the shared packages, you can run the admin application:

```bash
# From the root directory
npm run dev:admin

# Or from the admin directory
cd apps/admin
npm run dev
```

The admin dashboard will be available at: <http://localhost:3000>

### 3. Building for Production

To create a production build:

```bash
# From the root directory
npm run build:admin

# Or from the admin directory
cd apps/admin
npm run build:prod
```

### 4. Running Production Build

To run the production build:

```bash
# From the admin directory
cd apps/admin
npm run start:prod
```

The production build will be available at: <http://localhost:3000>

## Additional Commands

- `npm run lint` - Run ESLint to check for code issues
- `npm run lint:fix` - Run ESLint and automatically fix issues
- `npm run analyze` - Analyze bundle sizes (requires @next/bundle-analyzer)
