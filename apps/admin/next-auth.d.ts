import { AuthClinic } from "@jocom/types";
import { DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      clinics?: AuthClinic[];
      currentClinicId?: string;
      userId?: string;
      expires?: string;
    };
  }
  
  interface JWT extends DefaultJWT {
    clinics?: AuthClinic[];
    currentClinicId?: string;
    userId?: string;
    expires?: string;
  }
}