"use server";


import axiosInstance from "@jocom/config/src/axios/axios-instance";
import { AccountResume, ChuletazoResponse, ListAccounts, NewAccountData, NewUserClinicData, SingleAccountResume } from "@jocom/types";
import { AxiosResponse } from "axios";


export const getAllAccounts = async (
  limit = 20,
  offset = 0
): Promise<ListAccounts> => {

  const allAccounts: AxiosResponse<ChuletazoResponse<AccountResume[]>> =
    await axiosInstance.get(`/v1/account/list?limit=${limit}&offset=${offset}`);
    console.log("\n\n\n\n\nallAccounts");
  console.log(allAccounts.data);
  console.log(allAccounts.data.data[0].clinics[0]);

  return {accounts: allAccounts.data.data};
};

export const getSingleAccountByEmail = async (
  email: string
): Promise<SingleAccountResume> => {
  console.log('\n\n\n\n\nentrando entrando\n\n\n\n');
  
  const singleAccount:AxiosResponse<ChuletazoResponse<SingleAccountResume>> = await axiosInstance.get(`/v1/account/${encodeURIComponent(email)}`);
  
  return singleAccount.data.data;
};

export const createSingleAccount = async (
  newAccount: NewAccountData
): Promise<NewAccountData> => {
  const createSingleAccount: AxiosResponse<
    ChuletazoResponse<NewAccountData>
  > = await axiosInstance.post(`/v1/account`, newAccount);

  return createSingleAccount.data.data;
};

export const createUserInClinic = async (
  newUserClinic: NewUserClinicData
): Promise<NewUserClinicData> => {
  const createUserInClinic: AxiosResponse<
    ChuletazoResponse<NewUserClinicData>
  > = await axiosInstance.post(`/v1/account/clinic/user`, newUserClinic);

  return createUserInClinic.data.data;
};


export const deleteUserInClinic = async (
  newUserClinic: NewUserClinicData
): Promise<NewUserClinicData> => {
  const deletedUserInClinic: AxiosResponse<
    ChuletazoResponse<NewUserClinicData>
  > = await axiosInstance.delete(`/v1/account/clinic/user/${newUserClinic.clinicId}/${newUserClinic.email}`);

  return deletedUserInClinic.data.data;
};