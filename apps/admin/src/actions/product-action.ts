"use server";

import axiosInstance from "@jocom/config/src/axios/axios-instance";
import { AxiosResponse } from "axios";
import { ChuletazoResponse, ProductList } from "@jocom/types";



export const getAllProducts = async (limit=20, offset=0):Promise<ProductList> => {
    
    const allProducts:AxiosResponse<ChuletazoResponse<ProductList>> = await axiosInstance.get(`/v1/products/list?limit=${limit}&offset=${offset}`)

    console.log(allProducts.data.data);
    
    return allProducts.data.data;
}

