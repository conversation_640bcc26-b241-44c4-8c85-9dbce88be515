"use client";
import React, { useState } from "react";
import { SingleClinicResume } from "@jocom/types/dist/accounts/accounts";
import {
  InfoInBlocks,
  ItemInfoInBlocks,
} from "@jocom/ui/src/components/showInfo";
import {
  Table,
  TableBodyRow,
  TableItemActions,
  TableItemNameImage,
  TableItemText,
  MobileCardItem,
  MobileCardActions,
  MobileCardNameImage,
  MobileTableRow,
} from "@jocom/ui/src/components/table";
import {
  Modal,
  EmailInputModal,
  ConfirmationModal,
} from "@jocom/ui/src/components/modal";
import { H2 } from "@jocom/ui/src/components/text";
import { NumberOfGridColumns } from "@jocom/ui/src/components/constants";
import { PageState, usePageStore } from "@/stores/page-store";
import { ButtonWithArrow } from "@jocom/ui/src/components/buttons";
import { NewUserClinicData } from "@jocom/types";
import {
  createUserInClinic,
  deleteUserInClinic,
} from "@/actions/account-actions";

interface Props {
  singleClinic: SingleClinicResume;
  id: string;
}

interface UserForDelete {
  email?: string;
  name?: string;
}

const AccountClinic = ({ singleClinic, id }: Props) => {
  const { page, update } = usePageStore();
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userForDelete, setUserForDelete] = useState<UserForDelete>({});
  // Add a state to track the users
  const [users, setUsers] = useState(singleClinic.users);

  const handleAddUser = () => {
    setIsEmailModalOpen(true);
  };

  const handleEmailSubmit = async (userClinic: NewUserClinicData) => {
    const result: NewUserClinicData = await createUserInClinic(userClinic);

    console.log(
      `Adding user with email: ${result} to clinic: ${singleClinic.clinicId}`
    );

    // Create a new user object
    const newUser = {
      idUser: " ",
      name: result.name,
      photoUrl: "/avatar/avatar-05.svg",
      email: result.email,
      rol: result.rol,
      prefix: "",
    };

    // Update the state with the new user
    setUsers([...users, newUser]);

    // Show confirmation in the page modal
    const pageState: PageState = {
      error: false,
      message: `Se ha enviado una invitación a ${result.email}`,
      title: "Usuario invitado",
      show: true,
    };
    update(pageState);
  };

  const openDeleteModal = ({
    email,
    name,
  }: {
    email: string;
    name: string;
  }) => {
    setUserForDelete({ email, name });
    setIsDeleteModalOpen(true);
  };

  const handleDeleteUser = async () => {
    const deletedUser = await deleteUserInClinic({
      name: userForDelete.name as string,
      email: userForDelete.email as string,
      rol: "",
      clinicId: singleClinic.clinicId,
    });

    setUsers(users.filter((user) => user.email !== userForDelete.email));

    const pageState: PageState = {
      error: false,
      message: `Se ha enviado el usuario ${deletedUser.name} a la clinica ${singleClinic.name}`,
      title: "Usuario Eliminado",
      show: true,
    };

    setUserForDelete({});
    update(pageState);
  };

  const products: Array<ItemInfoInBlocks> = singleClinic.products.map(
    (product): ItemInfoInBlocks => ({
      title: `Producto`,
      text: product.name,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-5 h-5 text-blue-500"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"
          />
        </svg>
      ),
    })
  );

  const rows: Array<TableBodyRow> = [];
  const mobileRows: Array<MobileTableRow> = [];
  const headerNames: string[] = ["Nombre", "Rol", "Prefijo", "Acciones"];
  let count = 0;

  // Use the users state instead of singleClinic.users
  users.forEach((user) => {
    count++;

    const newRow: TableBodyRow = {
      id: `row-${count}`,
      items: [],
      key: `row-${count}`,
    };

    newRow.items.push(
      <TableItemNameImage
        name={user.name}
        email={user.email}
        urlImage={user.photoUrl}
        id={`${newRow.id}-00`}
        key={`${newRow.id}-00`}
      />
    );

    newRow.items.push(
      <TableItemText
        id={`${newRow.id}-01`}
        text={user.rol}
        key={`${newRow.id}-01`}
      />
    );

    newRow.items.push(
      <TableItemText
        id={`${newRow.id}-02`}
        text={user.prefix}
        key={`${newRow.id}-02`}
      />
    );

    newRow.items.push(
      <TableItemActions
        editUrl={null}
        onDelete={() => openDeleteModal({ email: user.email, name: user.name })}
        id={`${newRow.id}-04`}
        key={`${newRow.id}-04`}
      />
    );

    rows.push(newRow);

    // Mobile view row
    mobileRows.push({
      id: `mobile-row-${count}`,
      key: `mobile-row-${count}`,
      content: (
        <div>
          <MobileCardNameImage
            name={user.name}
            email={user.email}
            urlImage={user.photoUrl}
            size="md"
          />

          <MobileCardItem title="Rol" value={user.rol || "No asignado"} />

          <MobileCardItem
            title="Prefijo"
            value={user.prefix || "No asignado"}
          />

          <MobileCardActions>
            <button
              onClick={() =>
                openDeleteModal({ email: user.email, name: user.name })
              }
              className="p-2 text-red-600 hover:bg-red-50 rounded-full"
              aria-label="Eliminar usuario"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                />
              </svg>
            </button>
          </MobileCardActions>
        </div>
      ),
    });
  });

  return (
    <>
      <div key={singleClinic.clinicId}>
        {page.show && (
          <Modal
            onClose={function (): void {
              const page: PageState = {
                error: false,
                message: ``,
                title: "",
                show: false,
              };
              update(page);
            }}
          >
            <H2 text={page.title} />
            <p>{page.message}</p>
          </Modal>
        )}

        <H2 text={`CLINICA: ${singleClinic.name}`} />
        <InfoInBlocks
          numberOfGridColumns={NumberOfGridColumns.TWO}
          information={products}
          className="mt-4"
        />
        <Table
          headerNames={headerNames}
          rows={rows}
          id={id}
          mobileView={{
            enabled: true,
            title: `Usuarios - ${singleClinic.name}`,
            rows: mobileRows,
          }}
          breakpoint={768} // Switch to mobile view at 768px
        />
      </div>

      <ButtonWithArrow
        text={"Agregar usuario"}
        path={"#"}
        onClick={handleAddUser}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteUser}
        title="Confirmar eliminación"
        message={`¿Estás seguro que deseas eliminar al usuario ${userForDelete.name}? Esta acción no se puede deshacer.`}
        id={userForDelete.email as string}
      />

      <EmailInputModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        onSubmit={handleEmailSubmit}
        title="Agregar usuario a la clínica"
        description={`Ingrese el correo electrónico del usuario que desea agregar a la clínica ${singleClinic.name}`}
        submitText="Invitar usuario"
        clinicId={singleClinic.clinicId}
      />
    </>
  );
};

export default AccountClinic;
