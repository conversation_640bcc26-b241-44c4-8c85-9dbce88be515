"use client";

import { redirect } from "next/navigation";
import { PageState, usePageStore } from "@/stores/page-store";
import { NewAccountData } from "@jocom/types";
import { createSingleAccount } from "@/actions/account-actions";
import {
  FormNumberColumnsEnum,
  InputTypeEnum,
} from "@jocom/ui/src/components/constants";
import { Form, FormDiv, Input, Select } from "@jocom/ui/src/components/form";
import { H2 } from "@jocom/ui/src/components/text";

interface Props {
  productsResume: Array<{
    idProduct: string;
    name: string;
    payments: {
      idPayment: string;
      name: string;
      amount: number;
    }[];
  }>;
}

const NewAccount = ({ productsResume }: Props) => {
  const { update } = usePageStore();

  const productOptions: Array<{ id: string; text: string }> =
    productsResume.map((item) => ({ id: item.idProduct, text: item.name }));

  const handleCreateSingleAccount = async (formData: FormData) => {
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const idProduct = formData.get("idProduct") as string;
    const clinicName = formData.get("clinic") as string;

    const newAccountData: NewAccountData = {
      name,
      email,
      idProduct,
      clinicName,
    };
    const result = await createSingleAccount(newAccountData);

    if (result.email) {
      const page: PageState = {
        error: false,
        message: `Cuenta creada con éxito ${result.email}`,
        title: "Cuenta creada con éxito",
        show: true,
      };
      update(page);

      redirect(`/account/email?email=${encodeURIComponent(result.email)}`);
    }

    console.log(newAccountData);
    console.log(result);
  };

  return (
    <Form
      title={"Cuenta de  usuario"}
      description={"Ingrese los datos que se le solicitan."}
      submitText={"Guardar información"}
      actionForm={handleCreateSingleAccount}
    >
      <FormDiv numberOfColumns={FormNumberColumnsEnum.TWO}>
        <Input
          text={"Nombre del dueño de la cuenta"}
          placeholder={"Primer Nombre y primer apellido"}
          id={"name"}
          inputType={InputTypeEnum.TEXT}
        />

        <Input
          text={"Correo electrónico"}
          placeholder={"Escribe el correo electrónico"}
          id={"email"}
          inputType={InputTypeEnum.EMAIL}
        />
      </FormDiv>

      <H2 text="Ingrese información de la clinica" />
      <FormDiv numberOfColumns={FormNumberColumnsEnum.TWO}>
        <Input
          text={"Nombre de la clinica"}
          placeholder={"Escriba el nombre de la clinica"}
          id={"clinic"}
          inputType={InputTypeEnum.TEXT}
        />

        <Select
          id={"idProduct"}
          text={"Selecciona el producto para la clinica"}
          placeholder={"Esto se deberia de poder modificar"}
          options={productOptions}
        />
      </FormDiv>
    </Form>
  );
};

export default NewAccount;
