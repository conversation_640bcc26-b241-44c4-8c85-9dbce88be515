import React from "react";
import NewAccount from "./components/NewAccount";
import { getAllProducts } from "@/actions/product-action";
import { Product } from "@jocom/types";

const page = async () => {
  const allProducts = await getAllProducts();
  const productsResume: Array<{
    idProduct: string;
    name: string;
    payments: {
      idPayment: string;
      name: string;
      amount: number;
    }[];
  }> = allProducts.products.map((product: Product) => ({
    idProduct: product.idProduct,
    name: product.name + product.description.join(" - "),
    payments: product.payment,
  }));

  return (
    <>
      <NewAccount productsResume={productsResume} />
    </>
  );
};

export default page;
