import { getAllAccounts } from "@/actions/account-actions";
import { ButtonWithArrow } from "@jocom/ui/src/components/buttons";
import {
  ColorState,
  Table,
  TableBodyRow,
  TableItemActions,
  TableItemNameImage,
  TableItemState,
  TableItemText,
  MobileCardItem,
  MobileCardActions,
  MobileCardNameImage,
  MobileCardState,
  MobileTableRow,
} from "@jocom/ui/src/components/table";
import { H1 } from "@jocom/ui/src/components/text";
import React from "react";
import Link from "next/link";

const page = async () => {
  const allAccounts = await getAllAccounts();
  // Regular table rows
  const rows: Array<TableBodyRow> = [];
  // Mobile view rows
  const mobileRows: Array<MobileTableRow> = [];

  const headerNames: string[] = [
    "Nombre",
    "Estado",
    "Clinicas",
    "Pago",
    "Acciones",
  ];
  let count = 0;
  console.log("\n\n\n\n\nallAccounts");
  console.log(allAccounts.accounts.length);
  console.log(allAccounts.accounts);
  allAccounts.accounts.forEach((account) => {
    count++;
    const email = account.email;

    const newRow: TableBodyRow = {
      id: `row-${count}`,
      key: `row-${count}`,
      items: [],
    };
    newRow.items.push(
      <TableItemNameImage
        name={account.name ?? "patito"}
        email={account.email}
        urlImage={account.image ?? "/avatar/avatar-05.svg"}
        id={`${newRow.id}-00`}
        key={`${newRow.id}-00`}
      />
    );
    newRow.items.push(
      <TableItemState
        state={account.state}
        colorState={
          account.state === "activo" ? ColorState.GREEN : ColorState.RED
        }
        id={`${newRow.id}-01`}
        key={`${newRow.id}-01`}
      />
    );
    newRow.items.push(
      <TableItemText
        text={account.clinics.reduce(
          (accumulator, currentValue) =>
            accumulator +
            ` - ${currentValue.name}(${
              !currentValue.products ? "0" : currentValue.products.length
            }) `,
          ""
        )}
        id={`${newRow.id}-02`}
        key={`${newRow.id}-02`}
      />
    );
    newRow.items.push(
      <TableItemState
        state={account.billState ?? "al día"}
        colorState={
          account.billState === "al día" ? ColorState.GREEN : ColorState.RED
        }
        id={`${newRow.id}-03`}
        key={`${newRow.id}-03`}
      />
    );
    newRow.items.push(
      <TableItemActions
        editUrl={`/account/email?email=${encodeURIComponent(email)}`}
        id={`${newRow.id}-04`}
        key={`${newRow.id}-04`}
      />
    );
    rows.push(newRow);

    // Mobile view row
    mobileRows.push({
      id: `mobile-row-${count}`,
      key: `mobile-row-${count}`,
      content: (
        <div>
          <MobileCardNameImage
            name={account.name ?? "patito"}
            email={account.email}
            urlImage={account.image ?? "/avatar/avatar-05.svg"}
            size="md"
          />

          <MobileCardItem
            title="Estado"
            value={
              <MobileCardState
                state={account.state}
                colorState={
                  account.state === "activo" ? ColorState.GREEN : ColorState.RED
                }
              />
            }
          />

          <MobileCardItem
            title="Clinicas"
            value={
              <div className="text-sm">
                {account.clinics.map((clinic, idx) => (
                  <div key={idx} className="mb-1">
                    {clinic.name} (
                    {!clinic.products ? 0 : clinic.products.length})
                  </div>
                ))}
              </div>
            }
          />

          <MobileCardItem
            title="Pago"
            value={
              <MobileCardState
                state={account.billState ?? "al día"}
                colorState={
                  account.billState === "al día"
                    ? ColorState.GREEN
                    : ColorState.RED
                }
              />
            }
          />

          <MobileCardActions>
            <Link
              href={`/account/email?email=${encodeURIComponent(email)}`}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                />
              </svg>
            </Link>
          </MobileCardActions>
        </div>
      ),
    });
  });

  return (
    <>
      <H1 text={"Listado de cuentas"} />

      <Table
        headerNames={headerNames}
        rows={rows}
        id="Accounts-"
        mobileView={{
          enabled: true,
          title: "Cuentas",
          rows: mobileRows,
        }}
        breakpoint={768} // Switch to mobile view at 768px
      />
      <div className="mt-8 gap-3 flex justify-center">
        <ButtonWithArrow text={"Nueva Cuenta"} path={"/account/new"} />
      </div>
    </>
  );
};

export default page;
