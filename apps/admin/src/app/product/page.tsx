import { getAllProducts } from "@/actions/product-action";
import {
  ColorState,
  MobileCardItem,
  MobileCardActions,
  OptionNameColorState,
  Table,
  TableBodyRow,
  TableItemActions,
  TableItemManyOptions,
  TableItemText,
  MobileTableRow,
} from "@jocom/ui/src/components/table";
import { H1 } from "@jocom/ui/src/components/text";
import { NumberOfGridColumns } from "@jocom/ui/src/components/constants";
import React from "react";
import Link from "next/link";

const page = async () => {
  const allProducts = await getAllProducts();

  // Regular table rows
  const rows: Array<TableBodyRow> = [];
  // Mobile view rows
  const mobileRows: Array<MobileTableRow> = [];

  const headerNames: string[] = [
    "Nombre",
    "Descripción",
    "Usuarios",
    "Acciones",
  ];

  let count = 0;

  allProducts.products.forEach((product) => {
    count++;

    // Regular table row
    const newRow: TableBodyRow = {
      id: `raw-${count}-${product.idProduct}`,
      items: [],
      key: `raw-${count}-${product.idProduct}`,
    };

    newRow.items.push(
      <TableItemText
        text={product.name}
        id={`${newRow.id}-01`}
        key={`${newRow.id}-01`}
      />
    );

    newRow.items.push(
      <TableItemManyOptions
        id={`${newRow.id}-02`}
        options={product.description.map(
          (item): OptionNameColorState => ({
            name: item,
            colorState: ColorState.VIOLET,
          })
        )}
        numberOfGridColumns={NumberOfGridColumns.TWO}
        key={`${newRow.id}-02`}
      />
    );

    newRow.items.push(
      <TableItemText
        text={product.users + ""}
        id={`${newRow.id}-03`}
        key={`${newRow.id}-03`}
      />
    );

    newRow.items.push(
      <TableItemActions
        id={`${newRow.id}-04`}
        editUrl={`/product/${product.idProduct}`}
        key={`${newRow.id}-04`}
      />
    );

    rows.push(newRow);

    // Mobile view row
    mobileRows.push({
      id: `mobile-${count}-${product.idProduct}`,
      key: `mobile-${count}-${product.idProduct}`,
      content: (
        <div>
          <MobileCardItem
            title="Nombre"
            value={<div className="font-medium">{product.name}</div>}
          />

          <MobileCardItem
            title="Descripción"
            value={
              <div className="flex flex-wrap gap-1 mt-1">
                {product.description.map((item) => (
                  <span
                    key={item}
                    className="inline-flex items-center rounded-full bg-violet-50 px-2 py-1 text-xs font-semibold text-violet-600"
                  >
                    {item}
                  </span>
                ))}
              </div>
            }
          />

          <MobileCardItem title="Usuarios" value={product.users} />

          <MobileCardActions>
            <Link
              href={`/product/${product.idProduct}`}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                />
              </svg>
            </Link>
          </MobileCardActions>
        </div>
      ),
    });
  });

  return (
    <div>
      <H1 text={"Products"} />
      <Table
        headerNames={headerNames}
        rows={rows}
        id="products-table"
        mobileView={{
          enabled: true,
          title: "Products",
          rows: mobileRows,
        }}
        breakpoint={768} // Switch to mobile view at 768px
      />
    </div>
  );
};

export default page;
