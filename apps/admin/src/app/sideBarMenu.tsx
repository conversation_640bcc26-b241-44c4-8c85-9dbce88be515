import { AiOutlineProduct } from "react-icons/ai";
import { GiReceiveMoney } from "react-icons/gi";
import { GrShieldSecurity, GrConfigure } from "react-icons/gr";
import {
  MdOutlineDashboardCustomize,
  MdOutlineManageAccounts,
} from "react-icons/md";
import { TbReportAnalytics } from "react-icons/tb";
import type { SideBarMenu } from "@jocom/ui/src/components/layout";

export const sideBarMenu: SideBarMenu = [
  {
    subMenuText: "Aplicación",
    subMenuItems: [
      {
        path: "/",
        icon: <MdOutlineDashboardCustomize />,
        text: "Resumen",
        notification: null,
      },
      {
        path: "/account",
        icon: <MdOutlineManageAccounts />,
        text: "Cuentas",
        notification: null,
      },
      {
        path: "/product",
        icon: <AiOutlineProduct />,
        text: "Productos",
        notification: null,
      },
      {
        path: "/bill",
        icon: <GiReceiveMoney />,
        text: "Cobros",
        notification: null,
      },
      {
        path: "/report",
        icon: <TbReportAnalytics />,
        text: "Reportes",
        notification: null,
      },
    ],
  },
  {
    subMenuText: "Configuración",
    subMenuItems: [
      {
        path: "/security",
        icon: <GrShieldSecurity />,
        text: "Seguridad",
        notification: null,
      },
      {
        path: "/config",
        icon: <GrConfigure />,
        text: "Configuraciones",
        notification: null,
      },
    ],
  },
];
