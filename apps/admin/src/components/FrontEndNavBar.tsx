"use client";

import { useUserStore } from "@/stores/user-store";
import React from "react";
import { Navbar } from "@jocom/ui/src/components/layout";
import { signOut } from "next-auth/react";

function FrontEndNavBar() {
  const { user, reset } = useUserStore();

  // If user is not available yet, render nothing
  if (!user) {
    return null;
  }

  const handleLogout = () => {
    console.log("Logging out, clearing user store");
    reset();
    signOut({ callbackUrl: "/login" });
  };

  return (
    <Navbar
      navBarElement={null}
      navBarElement2={null}
      onLogout={handleLogout}
      user={user}
    />
  );
}

export default FrontEndNavBar;
