"use client";
import { useUserStore } from "@/stores/user-store";
import { StoredUser } from "@jocom/types";
import { useEffect } from "react";
import { useSession } from "next-auth/react";

export default function SessionProvider() {
  const { data: session, status } = useSession();
  const { update } = useUserStore();

  // Force update whenever session is available
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      console.log("Authenticated session available, updating store");

      // Create user data from session
      const userData: StoredUser = {
        id: session.user.userId || "",
        name: session.user.name || "",
        image: session.user.image || "",
        email: session.user.email || "",
        clinics: session.user.clinics || [],
        currentClinicId: session.user.currentClinicId || "",
        emailVerified: null,
      };

      // Always update the store when session is available
      console.log("Updating store with:", userData);
      update(userData);
    }
  }, [session, status, update]);

  return null;
}
