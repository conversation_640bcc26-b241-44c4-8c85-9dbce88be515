import { create } from "zustand";

export interface PageState {
  error: boolean;
  message: string;
  title: string;
  show: boolean;
}

interface PageStore {
  page: PageState;
  update: (page: PageState) => void;
}

export const usePageStore = create<PageStore>((set) => ({
  page: {
    error: false,
    message: "",
    title: "Testing",
    show: true,
  },
  update: (page) => set(() => ({ page })),
}));
