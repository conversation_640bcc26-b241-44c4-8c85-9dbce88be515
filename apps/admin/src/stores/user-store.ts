import { StoredUser } from "@jocom/types";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface UserStore {
  user: StoredUser | null;
  update: (user: StoredUser) => void;
  updateCurrentClinic: (clinicId: string) => void;
  reset: () => void;
}

// Use persist middleware to keep the store state across page reloads
export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      user: null,
      update: (user) => set({ user }),
      updateCurrentClinic: (clinicId) => 
        set((state) => ({
          user: state.user 
            ? { ...state.user, currentClinicId: clinicId } 
            : null
        })),
      reset: () => set({ user: null }),
    }),
    {
      name: "user-storage", // unique name for localStorage
    }
  )
);



/* 

    {
    userId: "1",
    token: "tokentoken",
    photoUrl: "/avatar/avatar-05.svg",
    firstName: "<PERSON>",
    lastName: "Sierra",
    prefix: "dr",
    currentClinicId: "1",
    clinics: [
      {
        name: "Clinica 1",
        clinicID: "1",
      },
      {
        name: "Clinica 2",
        clinicID: "2",
      },
    ],
    rol: ["01", "02", "03"],
    name: "",
    image: "",
    expires: ""
  },

    
*/