# Node modules
node_modules/
**/node_modules/

# Build output
dist/
**/dist/
.next/
**/.next/
out/
**/out/
build/
**/build/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# Env files
.env
.env.*
!.env.example



# OS files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
.vim/
*.sublime*

# TypeScript build info
*.tsbuildinfo
next-env.d.ts

# Coverage
coverage/
**/coverage/
.nyc_output/

# Optional: lockfiles (if you use only one package manager)
# yarn.lock
# pnpm-lock.yaml
# package-lock.json

# Turbo cache
turbo/
**/turbo/
.turbo/

# Next.js specific
.vercel/
.next/
next-env.d.ts

# Testing
playwright-report/
test-results/

# Yarn specific (if using Yarn)
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Cache
.cache/
.eslintcache
.stylelintcache

# Misc
.DS_Store
*.pem
