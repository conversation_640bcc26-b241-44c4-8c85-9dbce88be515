import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

// Rutas que no requieren autenticación
const publicPaths = [
  '/login',
  '/api/auth/signin',
  '/api/auth/callback',
  '/api/auth/signout',
  '/register',
  '/forgot-password',
  '/reset-password'
];

export async function middleware(request: NextRequest) {
  // Verificar si la ruta actual es pública
  const path = request.nextUrl.pathname;
  const isPublicPath = publicPaths.some(publicPath => 
    path === publicPath || path.startsWith(`${publicPath}/`)
  );

  // Verificar si hay archivos estáticos o API routes que no deberían ser protegidas
  const isStaticFile = path.match(/\.(js|css|png|jpg|jpeg|svg|ico|webp)$/);
  const isApiAuthRoute = path.startsWith('/api/auth/');
  
  // Si es una ruta pública, archivo estático o ruta de API de autenticación, permitir acceso
  if (isPublicPath || isStaticFile || isApiAuthRoute) {
    return NextResponse.next();
  }

  // Verificar el token de autenticación
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  });

  // Si no hay token, redirigir a la página de login
  if (!token) {
    const url = new URL('/login', request.url);
    url.searchParams.set('callbackUrl', encodeURI(request.url));
    return NextResponse.redirect(url);
  }

  // Configurar headers de seguridad
  const response = NextResponse.next();
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("Content-Security-Policy", "default-src 'self'");
  
  return response;
}

// Configurar en qué rutas se ejecutará el middleware
export const config = {
  matcher: [
    // Excluir archivos estáticos y rutas específicas
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
