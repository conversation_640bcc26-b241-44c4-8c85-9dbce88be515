{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001 --turbopack", "build": "next build", "build:prod": "NODE_ENV=production next build", "start": "next start -p 3001", "start:prod": "NODE_ENV=production next start -p 3000", "lint": "next lint", "analyze": "ANALYZE=true next build"}, "dependencies": {"@jocom/config": "^1.0.0", "@jocom/types": "^1.0.0", "@jocom/ui": "^1.0.0", "axios": "^1.9.0", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.50", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}