"use server";

import axiosInstance from "@jocom/config/src/axios/axios-instance";
import { AxiosResponse } from "axios";
import { Attendee, AttendeeUpdateStatus, ChuletazoResponse, Day, Events, EventsResponse } from "@jocom/types";
import { EventStatus } from "@jocom/ui/src/components/constants";
import { fixNumberTwoDigits } from "@/helpers/date-helpers";

const prefix = "/v1/calendar";

export const createCalendarEvent = async (
  event: Events,
  attendees: Attendee[]
): Promise<EventsResponse> => {
  const newEvent: AxiosResponse<ChuletazoResponse<EventsResponse>> =
    await axiosInstance.post(
      `${prefix}/${event.calendarId}/${event.createdBy}/events`,
      { ...event, attendees }
    );

  console.log(newEvent.data.data);

  return newEvent.data.data;
};

export const getCalendarEvents = async (
  calendarId: string,
  userId: string,
  day: Day
): Promise<EventsResponse[]> => {
  const events: AxiosResponse<ChuletazoResponse<EventsResponse[]>> =
    await axiosInstance.get(
      `${prefix}/${calendarId}/${userId}/${day.year}-${fixNumberTwoDigits(day.month)}-${fixNumberTwoDigits(day.day)}/events`
    );

    console.log("\n\n\n\nevents");
    
  console.log(events);
  

  return events.data.data;
};

export const getCalendarEvent = async (
  eventId: string,
  userId: string,
): Promise<EventsResponse> => {
  const events: AxiosResponse<ChuletazoResponse<EventsResponse>> =
    await axiosInstance.get(
      `${prefix}/${eventId}/${userId}/event`
    );

  return events.data.data;
};

export const  updateCalendarEvent = async(updatedEvent: Events, attendees: Attendee[], clinicId: string, userId: string):Promise<EventsResponse> => {
  
  const updated: AxiosResponse<ChuletazoResponse<EventsResponse>> =
    await axiosInstance.patch(
      `${prefix}/${clinicId}/${updatedEvent.id}/${userId}/event`,
      { ...updatedEvent, attendees }
    );

  return updated.data.data;
};

export const  updateAttendeeStatus = async (eventId: string, userId: string, status:EventStatus):Promise<Attendee[]> => {
const attendeeRequest:AttendeeUpdateStatus[] = [{
  userId,
  status: status as EventStatus
}]
  const updated: AxiosResponse<ChuletazoResponse<Attendee[]>> =
    await axiosInstance.patch(
      `${prefix}/events/${eventId}/attendees`,
      { attendees:  attendeeRequest }
    );

  return updated.data.data;
};