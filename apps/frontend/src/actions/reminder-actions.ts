"use server";

import axiosInstance from "@jocom/config/src/axios/axios-instance";
import { AxiosResponse } from "axios";
import { ChuletazoResponse, RemindersResponse, ReminderResponse, DeleteReminderResponse, EventReminder } from "@jocom/types";
import { ReminderMethod } from "@jocom/ui/src/components/constants";

const prefix = "/v1/reminders";

/**
 * Get all reminders for a user
 * GET /v1/reminders/users/{userId}
 */
export const getUserReminders = async (userId: string, date: string): Promise<RemindersResponse> => {
  const response: AxiosResponse<ChuletazoResponse<RemindersResponse>> = 
    await axiosInstance.get(`${prefix}/users/${userId}/${date}`);
  
  return response.data.data;
};

/**
 * Mark a reminder as viewed
 * PATCH /v1/reminders/viewed/{reminderId}
 */
export const markReminderAsViewed = async (reminderId: string): Promise<ReminderResponse> => {
  const viewedAt = new Date().toISOString();
  
  const response: AxiosResponse<ChuletazoResponse<ReminderResponse>> = 
    await axiosInstance.patch(`${prefix}/viewed/${reminderId}`, {
      viewedAt
    });
  
  return response.data.data;
};

/**
 * Dismiss a reminder
 * PATCH /v1/reminders/dismiss/{reminderId}
 */
export const dismissReminder = async (reminderId: string): Promise<ReminderResponse> => {
  const dismissedAt = new Date().toISOString();
  
  const response: AxiosResponse<ChuletazoResponse<ReminderResponse>> = 
    await axiosInstance.patch(`${prefix}/dismiss/${reminderId}`, {
      dismissedAt
    });
  
  return response.data.data;
};

/**
 * Create a reminder for an event
 * POST /v1/reminders/events/{eventId}
 */
export const createEventReminder = async (
  eventId: string,
  userId: string,
  calendarId: string,
  remindAt: string,
  method: ReminderMethod = ReminderMethod.IN_APP
): Promise<EventReminder> => {
  const response: AxiosResponse<ChuletazoResponse<EventReminder>> = 
    await axiosInstance.post(`${prefix}/events/${eventId}`, {
      userId,
      calendarId,
      remindAt,
      method
    });
  
  return response.data.data;
};

/**
 * Delete a reminder
 * DELETE /v1/reminders/{reminderId}
 */
export const deleteReminder = async (reminderId: string): Promise<DeleteReminderResponse> => {
  const response: AxiosResponse<ChuletazoResponse<DeleteReminderResponse>> = 
    await axiosInstance.delete(`${prefix}/reminders/${reminderId}`);
  
  return response.data.data;
};

/**
 * Get reminder history for a user
 * GET /v1/reminders/users/{userId}/history/{date}
 */
export const getUserReminderHistory = async (
  userId: string, 
  date: string
): Promise<RemindersResponse> => {
  const response: AxiosResponse<ChuletazoResponse<RemindersResponse>> = 
    await axiosInstance.get(`${prefix}/users/${userId}/history/${date}`);
  
  return response.data.data;
};

/**
 * Update reminder method
 * PATCH /v1/reminders/{reminderId}/method
 */
export const updateReminderMethod = async (
  reminderId: string, 
  method: ReminderMethod
): Promise<ReminderResponse> => {
  const response: AxiosResponse<ChuletazoResponse<ReminderResponse>> = 
    await axiosInstance.patch(`${prefix}/${reminderId}/method`, {
      method
    });
  
  return response.data.data;
};

/**
 * Update reminder time
 * PATCH /v1/reminders/{reminderId}/time
 */
export const updateReminderTime = async (
  reminderId: string, 
  remindAt: string
): Promise<ReminderResponse> => {
  const response: AxiosResponse<ChuletazoResponse<ReminderResponse>> = 
    await axiosInstance.patch(`${prefix}/${reminderId}/time`, {
      remindAt
    });
  
  return response.data.data;
};
