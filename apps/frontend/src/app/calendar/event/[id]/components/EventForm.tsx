"use client";
import { EventsResponse, AttendeeResponse, Events } from "@jocom/types";
import React, { useState, useEffect } from "react";
import {
  Form,
  FormDiv,
  Input,
  Select,
  InputTextArea,
  DatePicker,
  TimePicker,
} from "@jocom/ui/src/components/form";
import {
  FormNumberColumnsEnum,
  InputTypeEnum,
  EventStatus,
} from "@jocom/ui/src/components/constants";
import { MdPersonRemoveAlt1 } from "react-icons/md";
import {
  updateCalendarEvent,
  updateAttendeeStatus,
} from "@/actions/calendar-actions";
import { useRouter } from "next/navigation";

interface EventFormProps {
  event: EventsResponse;
  userId: string;
  clinicId: string;
}

function updateTime(date: Date, time: Date): Date {
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    time.getHours(),
    time.getMinutes(),
    0,
    0
  );
}

function EventForm({ event, userId, clinicId }: EventFormProps) {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [attendees, setAttendees] = useState<AttendeeResponse[]>(
    event.attendees || []
  );
  const [newAttendeeEmail, setNewAttendeeEmail] = useState("");
  const [userAttendance, setUserAttendance] = useState<EventStatus>(
    EventStatus.INVITED
  );

  // Initialize event date from event startTime
  const [eventDate, setEventDate] = useState<Date>(() => {
    return new Date(event.startTime);
  });

  // Initialize start time from event startTime
  const [startTime, setStartTime] = useState<Date>(() => {
    return new Date(event.startTime);
  });

  // Initialize end time from event endTime
  const [endTime, setEndTime] = useState<Date>(() => {
    return new Date(event.endTime);
  });

  // Find current user's attendance status
  useEffect(() => {
    const userAttendee = attendees.find(
      (attendee) => attendee.userId === userId
    );
    if (userAttendee) {
      setUserAttendance(userAttendee.status as EventStatus);
    }
  }, [attendees, userId]);

  // Check if user is the event creator
  const isCreator = event.createdBy === userId;

  // Handle date change
  const handleDateChange = (date: Date) => {
    setEventDate(date);
  };

  // Handle start time change
  const handleStartTimeChange = (time: Date) => {
    setStartTime(time);

    // If end time is now before start time, update it to be 30 minutes later
    const newStartDateTime = updateTime(eventDate, time);
    const newEndDateTime = updateTime(eventDate, endTime);

    if (newEndDateTime <= newStartDateTime) {
      const adjustedEndTime = new Date(time);
      adjustedEndTime.setMinutes(time.getMinutes() + 30);
      setEndTime(adjustedEndTime);
    }
  };

  // Handle end time change
  const handleEndTimeChange = (time: Date) => {
    // Only accept if it would be after the start time when combined with the event date
    const newStartDateTime = updateTime(eventDate, startTime);
    const newEndDateTime = updateTime(eventDate, time);

    if (newEndDateTime > newStartDateTime) {
      setEndTime(time);
    } else {
      // If it would be before start time, set to 30 minutes after start
      const adjustedEndTime = new Date(startTime);
      adjustedEndTime.setMinutes(startTime.getMinutes() + 30);
      setEndTime(adjustedEndTime);
    }
  };

  const addAttendee = () => {
    if (newAttendeeEmail.trim() === "") return;

    const newAttendee: AttendeeResponse = {
      eventId: event.id,
      userId: newAttendeeEmail, // Using email as temporary userId
      name: "",
      email: newAttendeeEmail,
      status: EventStatus.INVITED,
      photoUrl: "",
    };

    setAttendees([...attendees, newAttendee]);
    setNewAttendeeEmail("");
  };

  const removeAttendee = (index: number) => {
    const updatedAttendees = [...attendees];
    updatedAttendees.splice(index, 1);
    setAttendees(updatedAttendees);
  };

  const handleUpdateAttendance = async (status: EventStatus) => {
    try {
      await updateAttendeeStatus(event.id, userId, status);
      setUserAttendance(status);

      // Update the attendees list
      const updatedAttendees = attendees.map((attendee) =>
        attendee.userId === userId ? { ...attendee, status } : attendee
      );

      setAttendees(updatedAttendees);
    } catch (error) {
      console.error("Failed to update attendance status:", error);
    }
  };

  const handleSubmit = async (formData: FormData) => {
    if (!isCreator) return;

    const title = formData.get("title") as string;
    const description = (formData.get("description") as string) || "";
    const location = (formData.get("location") as string) || "";
    const isAllDay = formData.get("isAllDay") === "true";

    // Combine date and times to create start and end datetime
    const startDateTime = updateTime(eventDate, startTime);
    const endDateTime = updateTime(eventDate, endTime);

    const updatedEvent: Events = {
      ...event,
      title,
      description,
      location,
      isAllDay,
      startTime: startDateTime.toISOString(),
      endTime: endDateTime.toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      setIsEditing(false);
      await updateCalendarEvent(updatedEvent, attendees, clinicId, userId);
      router.refresh();
    } catch (error) {
      console.error("Failed to update event:", error);
    }
  };

  // Helper function to get status display text
  const getStatusDisplayText = (status: string): string => {
    switch (status) {
      case EventStatus.ACCEPTED:
        return "Asistirá";
      case EventStatus.DECLINED:
        return "No asistirá";
      case EventStatus.MAYBE:
        return "Tal vez";
      case EventStatus.INVITED:
      default:
        return "Invitado";
    }
  };

  // Helper function to get status badge colors
  const getStatusBadgeColors = (status: string): string => {
    switch (status) {
      case EventStatus.ACCEPTED:
        return "bg-green-100 text-green-800";
      case EventStatus.DECLINED:
        return "bg-red-100 text-red-800";
      case EventStatus.MAYBE:
        return "bg-yellow-100 text-yellow-800";
      case EventStatus.INVITED:
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {isEditing ? "Editar Evento" : event.title}
        </h1>
        {isCreator && !isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Editar Evento
          </button>
        )}
      </div>

      {isEditing ? (
        <Form
          title="Editar evento"
          description="Actualice los detalles del evento"
          submitText="Guardar cambios"
          actionForm={handleSubmit}
        >
          <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
            <Input
              text="Título"
              placeholder="Título del evento"
              id="title"
              inputType={InputTypeEnum.TEXT}
              value={event.title}
              required={true}
            />

            <InputTextArea
              text="Descripción"
              placeholder="Descripción del evento"
              id="description"
              value={event.description || ""}
            />

            <DatePicker
              id="event-date"
              label="Fecha del evento"
              initialDate={eventDate}
              onChange={handleDateChange}
              required={true}
            />

            <div className="flex space-x-4">
              <div className="w-1/2">
                <TimePicker
                  id="start-time"
                  label="Hora de inicio"
                  initialTime={startTime}
                  onChange={handleStartTimeChange}
                  required={true}
                />
              </div>
              <div className="w-1/2">
                <TimePicker
                  id="end-time"
                  label="Hora de fin"
                  initialTime={endTime}
                  onChange={handleEndTimeChange}
                  required={true}
                />
              </div>
            </div>

            <div className="flex items-center mt-2">
              <Select
                id={"isAllDay"}
                text={"Todo el día"}
                placeholder={"Es todo el dia"}
                options={[
                  { id: "true", text: "Si" },
                  { id: "false", text: "No" },
                ]}
                value={event.isAllDay ? "true" : "false"}
              />
            </div>

            <Input
              text="Ubicación"
              placeholder="Ubicación del evento"
              id="location"
              inputType={InputTypeEnum.TEXT}
              value={event.location || ""}
              required={false}
            />

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Invitar asistentes
              </label>
              <div className="flex mt-1">
                <input
                  type="email"
                  placeholder="Correo electrónico"
                  value={newAttendeeEmail}
                  onChange={(e) => setNewAttendeeEmail(e.target.value)}
                  className="flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
                <button
                  type="button"
                  onClick={addAttendee}
                  className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Agregar
                </button>
              </div>

              {attendees.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Asistentes:
                  </h4>
                  <ul className="mt-2 space-y-2">
                    {attendees.map((attendee, index) => (
                      <li
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md"
                      >
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {attendee.email || attendee.userId}
                          <span className="ml-2 text-xs px-2 py-1 rounded bg-gray-200 dark:bg-gray-700">
                            {getStatusDisplayText(attendee.status)}
                          </span>
                        </span>
                        <button
                          type="button"
                          onClick={() => removeAttendee(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <MdPersonRemoveAlt1 className="h-5 w-5" />
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </FormDiv>
        </Form>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">{event.title}</h2>
            <p className="text-gray-600 dark:text-gray-300">
              {event.description}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Fecha y hora
              </h3>
              <p className="mt-1">
                {new Date(event.startTime).toLocaleDateString()} •
                {new Date(event.startTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}{" "}
                -
                {new Date(event.endTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>

            {event.location && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Ubicación
                </h3>
                <p className="mt-1">{event.location}</p>
              </div>
            )}
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              Asistentes
            </h3>
            <ul className="space-y-2">
              {attendees.map((attendee, index) => (
                <li
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
                >
                  <span>{attendee.email || attendee.userId}</span>
                  <span
                    className={`px-2 py-1 text-xs rounded ${getStatusBadgeColors(
                      attendee.status
                    )}`}
                  >
                    {getStatusDisplayText(attendee.status)}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div className="border-t pt-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
              ¿Asistirás a este evento?
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={() => handleUpdateAttendance(EventStatus.ACCEPTED)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  userAttendance === EventStatus.ACCEPTED
                    ? "bg-green-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-green-100"
                }`}
              >
                Sí, asistiré
              </button>
              <button
                onClick={() => handleUpdateAttendance(EventStatus.MAYBE)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  userAttendance === EventStatus.MAYBE
                    ? "bg-yellow-500 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-yellow-100"
                }`}
              >
                Tal vez
              </button>
              <button
                onClick={() => handleUpdateAttendance(EventStatus.DECLINED)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  userAttendance === EventStatus.DECLINED
                    ? "bg-red-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-red-100"
                }`}
              >
                No asistiré
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default EventForm;
