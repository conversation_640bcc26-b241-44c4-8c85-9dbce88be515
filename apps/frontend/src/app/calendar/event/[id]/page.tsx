import React from "react";
import EventForm from "./components/EventForm";
import { getCalendarEvent } from "@/actions/calendar-actions";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

const page = async ({ params }: { params: { id: string } }) => {
  // Get the session and authenticate
  const session = await auth();

  if (!session || !session.user) {
    redirect("/api/auth/signin");
  }

  const userId = session.user.userId || "";
  const clinicId = session.user.currentClinicId || "";

  const { id } = await params;

  // Fetch the event data
  const event = await getCalendarEvent(id, userId);

  console.log(`Fetched event ${id} for user ${userId} `, event);

  return <EventForm event={event} userId={userId} clinicId={clinicId} />;
};

export default page;
