import React from "react";
import { ReminderMethod } from "@jocom/ui/src/components/constants";

interface ReminderMethodSelectorProps {
  currentMethod: ReminderMethod;
  onChange: (method: ReminderMethod) => void;
}

const ReminderMethodSelector: React.FC<ReminderMethodSelectorProps> = ({
  currentMethod,
  onChange,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value as ReminderMethod);
  };

  return (
    <select
      value={currentMethod}
      onChange={handleChange}
      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
    >
      <option value={ReminderMethod.IN_APP}>In App</option>
      <option value={ReminderMethod.EMAIL}>Email</option>
      <option value={ReminderMethod.PUSH}>Push Notification</option>
    </select>
  );
};

export default ReminderMethodSelector;
