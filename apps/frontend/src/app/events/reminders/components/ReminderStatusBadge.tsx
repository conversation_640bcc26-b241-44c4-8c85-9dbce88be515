import React from "react";
import { ReminderStatus } from "@jocom/ui/src/components/constants";

interface ReminderStatusBadgeProps {
  status: ReminderStatus;
}

const ReminderStatusBadge: React.FC<ReminderStatusBadgeProps> = ({
  status,
}) => {
  const getStatusStyles = () => {
    switch (status) {
      case ReminderStatus.PENDING:
        return "bg-yellow-100 text-yellow-800";
      case ReminderStatus.SENT:
        return "bg-blue-100 text-blue-800";
      case ReminderStatus.VIEWED:
        return "bg-green-100 text-green-800";
      case ReminderStatus.DISMISSED:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = () => {
    switch (status) {
      case ReminderStatus.PENDING:
        return "Pending";
      case ReminderStatus.SENT:
        return "Delivered";
      case ReminderStatus.VIEWED:
        return "Viewed";
      case ReminderStatus.DISMISSED:
        return "Dismissed";
      default:
        return "Unknown";
    }
  };

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyles()}`}
    >
      {getStatusText()}
    </span>
  );
};

export default ReminderStatusBadge;
