"use client";

import React, { useEffect, useState } from "react";
import {
  Table,
  TableBodyRow,
  TableItemText,
  TableItemActions,
  MobileCardItem,
  MobileCardActions,
  MobileTableRow,
} from "@jocom/ui/src/components/table";
import { EventReminder } from "@jocom/types";
import {
  getUserReminders,
  dismissReminder,
  deleteReminder,
  updateReminderMethod,
  updateReminderTime,
} from "@/actions/reminder-actions";
import {
  ReminderMethod,
  ReminderStatus,
} from "@jocom/ui/src/components/constants";
import { useUserStore } from "@/stores/user-store";
import ReminderMethodSelector from "./ReminderMethodSelector";
import ReminderDateTimePicker from "./ReminderDateTimePicker";
import ReminderStatusBadge from "./ReminderStatusBadge";
import { formatDate, formatTime } from "@/helpers/date-helpers";

const ReminderTable = () => {
  const [reminders, setReminders] = useState<EventReminder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUserStore();

  useEffect(() => {
    const fetchReminders = async () => {
      try {
        setLoading(true);
        // Format current date as YYYY-MM-DD
        const today = new Date().toISOString().split("T")[0];
        const response = await getUserReminders(user?.id || "", today);
        setReminders(response.reminders);
      } catch (err) {
        console.error("Failed to fetch reminders:", err);
        setError("Failed to load reminders. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    if (user?.id) {
      fetchReminders();
    }
  }, [user?.id]);

  const handleDismissReminder = async (reminderId: string) => {
    try {
      await dismissReminder(reminderId);
      // Update local state
      setReminders((prevReminders) =>
        prevReminders.map((reminder) =>
          reminder.id === reminderId
            ? {
                ...reminder,
                dismissedAt: new Date().toISOString(),
                status: ReminderStatus.DISMISSED,
              }
            : reminder
        )
      );
    } catch (err) {
      console.error("Failed to dismiss reminder:", err);
      setError("Failed to dismiss reminder. Please try again.");
    }
  };

  const handleDeleteReminder = async (reminderId: string) => {
    try {
      await deleteReminder(reminderId);
      // Remove from local state
      setReminders((prevReminders) =>
        prevReminders.filter((reminder) => reminder.id !== reminderId)
      );
    } catch (err) {
      console.error("Failed to delete reminder:", err);
      setError("Failed to delete reminder. Please try again.");
    }
  };

  const handleMethodChange = async (
    reminderId: string,
    method: ReminderMethod
  ) => {
    try {
      // Update local state first for immediate feedback
      setReminders((prevReminders) =>
        prevReminders.map((reminder) =>
          reminder.id === reminderId ? { ...reminder, method } : reminder
        )
      );

      // Call the API to update the method
      await updateReminderMethod(reminderId, method);
    } catch (err) {
      console.error("Failed to update reminder method:", err);
      setError("Failed to update reminder method. Please try again.");
    }
  };

  const handleReminderTimeChange = async (
    reminderId: string,
    newDateTime: Date
  ) => {
    try {
      // Update local state first for immediate feedback
      setReminders((prevReminders) =>
        prevReminders.map((reminder) =>
          reminder.id === reminderId
            ? { ...reminder, remindAt: newDateTime.toISOString() }
            : reminder
        )
      );

      // Call the API to update the reminder time
      await updateReminderTime(reminderId, newDateTime.toISOString());
    } catch (err) {
      console.error("Failed to update reminder time:", err);
      setError("Failed to update reminder time. Please try again.");
    }
  };

  if (loading) {
    return <div className="flex justify-center p-8">Loading reminders...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  // Prepare table rows for desktop view
  const tableRows: TableBodyRow[] = reminders.map((reminder) => ({
    id: `row-${reminder.id}`,
    key: `row-${reminder.id}`,
    items: [
      <TableItemText
        id={`event-${reminder.id}`}
        key={`event-${reminder.id}`}
        text={reminder.eventId}
      />,
      <TableItemText
        id={`date-${reminder.id}`}
        key={`date-${reminder.id}`}
        text={formatDate(reminder.remindAt)}
      />,
      <TableItemText
        id={`time-${reminder.id}`}
        key={`time-${reminder.id}`}
        text={formatTime(reminder.remindAt)}
      />,
      <div
        id={`status-${reminder.id}`}
        key={`status-${reminder.id}`}
        className="px-6 py-4"
      >
        <ReminderStatusBadge status={reminder.status} />
      </div>,
      <div
        id={`method-${reminder.id}`}
        key={`method-${reminder.id}`}
        className="px-6 py-4"
      >
        <ReminderMethodSelector
          currentMethod={reminder.method}
          onChange={(method) => handleMethodChange(reminder.id, method)}
        />
      </div>,
      <div
        id={`datetime-${reminder.id}`}
        key={`datetime-${reminder.id}`}
        className="px-6 py-4"
      >
        <ReminderDateTimePicker
          currentDateTime={new Date(reminder.remindAt)}
          onChange={(date) => handleReminderTimeChange(reminder.id, date)}
        />
      </div>,
      <TableItemActions
        id={`actions-${reminder.id}`}
        key={`actions-${reminder.id}`}
        editUrl={null}
        onDelete={() => {
          handleDeleteReminder(reminder.id);
        }}
        customActions={
          <button
            onClick={() => handleDismissReminder(reminder.id)}
            disabled={reminder.status === ReminderStatus.DISMISSED}
            className={`p-2 text-blue-600 hover:bg-blue-50 rounded-full ${
              reminder.status === ReminderStatus.DISMISSED
                ? "opacity-50 cursor-not-allowed"
                : ""
            }`}
            title="Dismiss reminder"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </button>
        }
      />,
    ],
  }));

  // Prepare mobile view rows
  const mobileRows: MobileTableRow[] = reminders.map((reminder) => ({
    id: `mobile-row-${reminder.id}`,
    key: `mobile-row-${reminder.id}`,
    content: (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="font-medium">Event ID: {reminder.eventId}</div>
          <ReminderStatusBadge status={reminder.status} />
        </div>

        <MobileCardItem
          title="Remind At"
          value={`${formatDate(reminder.remindAt)} at ${formatTime(
            reminder.remindAt
          )}`}
        />

        <MobileCardItem
          title="Reminder Method"
          value={
            <ReminderMethodSelector
              currentMethod={reminder.method}
              onChange={(method) => handleMethodChange(reminder.id, method)}
            />
          }
        />

        <MobileCardItem
          title="Change Time"
          value={
            <ReminderDateTimePicker
              currentDateTime={new Date(reminder.remindAt)}
              onChange={(date) => handleReminderTimeChange(reminder.id, date)}
              isMobile={true}
            />
          }
        />

        <MobileCardActions>
          <button
            onClick={() => handleDismissReminder(reminder.id)}
            disabled={reminder.status === ReminderStatus.DISMISSED}
            className={`p-2 text-blue-600 hover:bg-blue-50 rounded-full ${
              reminder.status === ReminderStatus.DISMISSED
                ? "opacity-50 cursor-not-allowed"
                : ""
            }`}
            title="Dismiss reminder"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </button>

          <button
            onClick={() => handleDeleteReminder(reminder.id)}
            className="p-2 text-red-600 hover:bg-red-50 rounded-full"
            title="Delete reminder"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
              />
            </svg>
          </button>
        </MobileCardActions>
      </div>
    ),
  }));

  const headerNames = [
    "Event",
    "Date",
    "Time",
    "Status",
    "Method",
    "Change Time",
    "Actions",
  ];

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Event Reminders</h1>

      {reminders.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <p className="text-gray-600 dark:text-gray-300">
            No reminders found.
          </p>
        </div>
      ) : (
        <Table
          headerNames={headerNames}
          rows={tableRows}
          id="reminders-table"
          mobileView={{
            enabled: true,
            title: "Event Reminders",
            rows: mobileRows,
          }}
          breakpoint={768}
        />
      )}
    </div>
  );
};

export default ReminderTable;
