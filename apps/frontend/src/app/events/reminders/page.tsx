import React from "react";
import ReminderTable from "./components/ReminderTable";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

const RemindersPage = async () => {
  // Get the session and authenticate
  const session = await auth();

  if (!session || !session.user) {
    redirect("/api/auth/signin");
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Event Reminders</h1>
      <p className="text-gray-600 dark:text-gray-300 mb-8">
        Manage your event reminders and notifications.
      </p>

      <ReminderTable />
    </div>
  );
};

export default RemindersPage;
