import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Sidebar } from "@jocom/ui/src/components/layout";
import { sideBarMenu } from "./sideBarMenu";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { SessionProvider } from "next-auth/react";
import SessionStoreUpdater from "@/components/SessionProvider";
import FrontEndNavBar from "@/components/FrontEndNavBar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();

  if (!session) {
    redirect("api/auth/signin");
  }

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider session={session}>
          <SessionStoreUpdater />
          <div className="min-h-screen flex flex-col flex-auto shrink-0 antialiased text-black dark:text-white">
            <FrontEndNavBar />
            <Sidebar sideBarMenu={sideBarMenu} />
            <div className="h-full ml-14 mt-14 mb-10 md:ml-64">{children}</div>
          </div>
        </SessionProvider>
      </body>
    </html>
  );
}
