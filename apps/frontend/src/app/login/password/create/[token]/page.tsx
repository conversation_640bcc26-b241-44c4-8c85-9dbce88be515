"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Form, FormDiv } from "@jocom/ui/src/components/form";
import { FormNumberColumnsEnum } from "@jocom/ui/src/components/constants";

interface PasswordValidation {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
}

interface FormErrors {
  password: string;
  confirmPassword: string;
  general: string;
}

export default function CreatePasswordPage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [validation, setValidation] = useState<PasswordValidation>({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
  });
  const [errors, setErrors] = useState<FormErrors>({
    password: "",
    confirmPassword: "",
    general: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Print token to console when component mounts
  useEffect(() => {
    console.log("Token from URL:", token);
  }, [token]);

  // Validate password strength
  const validatePassword = (pwd: string): PasswordValidation => {
    return {
      minLength: pwd.length >= 8,
      hasUppercase: /[A-Z]/.test(pwd),
      hasLowercase: /[a-z]/.test(pwd),
      hasNumber: /\d/.test(pwd),
      hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd),
    };
  };

  // Handle password input change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setValidation(validatePassword(newPassword));

    // Clear password error when user starts typing
    if (errors.password) {
      setErrors((prev) => ({ ...prev, password: "" }));
    }
  };

  // Handle confirm password input change
  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);

    // Clear confirm password error when user starts typing
    if (errors.confirmPassword) {
      setErrors((prev) => ({ ...prev, confirmPassword: "" }));
    }
  };

  // Check if password meets all requirements
  const isPasswordValid = (): boolean => {
    return Object.values(validation).every(Boolean);
  };

  // Validate form before submission
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {
      password: "",
      confirmPassword: "",
      general: "",
    };

    // Validate password strength
    if (!password) {
      newErrors.password = "La contraseña es requerida";
    } else if (!isPasswordValid()) {
      newErrors.password =
        "La contraseña no cumple con los requisitos de seguridad";
    }

    // Validate password confirmation
    if (!confirmPassword) {
      newErrors.confirmPassword = "Debe confirmar la contraseña";
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = "Las contraseñas no coinciden";
    }

    setErrors(newErrors);
    return !newErrors.password && !newErrors.confirmPassword;
  };

  // Handle form submission
  const handleSubmit = async () => {
    console.log("Form submitted with token:", token);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors((prev) => ({ ...prev, general: "" }));

    try {
      // TODO: Replace with actual API call
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password,
        }),
      });

      if (response.ok) {
        // Success - redirect to login
        router.push("/login?message=password-reset-success");
      } else {
        const errorData = await response.json();
        setErrors((prev) => ({
          ...prev,
          general:
            errorData.message ||
            "Error al crear la contraseña. Intente nuevamente.",
        }));
      }
    } catch (error) {
      console.error("Error creating password:", error);
      setErrors((prev) => ({
        ...prev,
        general: "Error de conexión. Intente nuevamente.",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Form
          title="Crear Nueva Contraseña"
          description="Ingrese su nueva contraseña. Debe cumplir con los requisitos de seguridad."
          submitText={isLoading ? "Creando..." : "Crear Contraseña"}
          actionForm={handleSubmit}
          disabled={() => {
            return !isPasswordValid() || password !== confirmPassword;
          }}
        >
          <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
            {/* Password Input */}
            <div className="relative">
              <label
                htmlFor="password"
                className="block text-sm font-semibold leading-6"
              >
                Nueva Contraseña
              </label>
              <div className="mt-2.5 relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={password}
                  onChange={handlePasswordChange}
                  placeholder="Ingrese su nueva contraseña"
                  className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  {showPassword ? "🙈" : "👁️"}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password Input */}
            <div className="relative">
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-semibold leading-6"
              >
                Confirmar Contraseña
              </label>
              <div className="mt-2.5">
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  placeholder="Confirme su nueva contraseña"
                  className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.confirmPassword}
                </p>
              )}
            </div>

            {/* Password Requirements */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Requisitos de la contraseña:
              </h4>
              <ul className="space-y-1 text-sm">
                <li
                  className={`flex items-center ${
                    validation.minLength ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  <span className="mr-2">
                    {validation.minLength ? "✓" : "○"}
                  </span>
                  Mínimo 8 caracteres
                </li>
                <li
                  className={`flex items-center ${
                    validation.hasUppercase ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  <span className="mr-2">
                    {validation.hasUppercase ? "✓" : "○"}
                  </span>
                  Al menos una letra mayúscula
                </li>
                <li
                  className={`flex items-center ${
                    validation.hasLowercase ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  <span className="mr-2">
                    {validation.hasLowercase ? "✓" : "○"}
                  </span>
                  Al menos una letra minúscula
                </li>
                <li
                  className={`flex items-center ${
                    validation.hasNumber ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  <span className="mr-2">
                    {validation.hasNumber ? "✓" : "○"}
                  </span>
                  Al menos un número
                </li>
                <li
                  className={`flex items-center ${
                    validation.hasSpecialChar
                      ? "text-green-600"
                      : "text-gray-500"
                  }`}
                >
                  <span className="mr-2">
                    {validation.hasSpecialChar ? "✓" : "○"}
                  </span>
                  Al menos un carácter especial (!@#$%^&amp;*()_+-=[]{`{}`}
                  |;:,.&lt;&gt;?)
                </li>
              </ul>
            </div>

            {/* Password Match Indicator */}
            {password && confirmPassword && (
              <div className="mt-2">
                {password === confirmPassword ? (
                  <p className="text-sm text-green-600 flex items-center">
                    <span className="mr-2">✓</span>
                    Las contraseñas coinciden
                  </p>
                ) : (
                  <p className="text-sm text-red-600 flex items-center">
                    <span className="mr-2">✗</span>
                    Las contraseñas no coinciden
                  </p>
                )}
              </div>
            )}

            {/* General Error Message */}
            {errors.general && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}
          </FormDiv>
        </Form>
        {/* Back to Login Link */}
        <div className="mt-4 text-center">
          <button
            type="button"
            onClick={() => router.push("/login")}
            className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Volver al inicio de sesión
          </button>
        </div>
      </div>
    </div>
  );
}
