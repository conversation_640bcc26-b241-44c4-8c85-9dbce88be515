import { RemindersResponse } from "@jocom/types";
import {
  <PERSON>minderMethod,
  ReminderStatus,
} from "@jocom/ui/src/components/constants";
import type { SideBarMenu } from "@jocom/ui/src/components/layout";
import { FaHome } from "react-icons/fa";
import { FaCalendarDays } from "react-icons/fa6";
import { HiDocumentCheck } from "react-icons/hi2";
import { ImProfile } from "react-icons/im";
import { IoMdSettings } from "react-icons/io";

export const sideBarMenu: SideBarMenu = [
  {
    subMenuText: "Aplicación",
    subMenuItems: [
      {
        path: "/",
        text: "Inicio",
        notification: null,
        icon: <FaHome />,
      },
      {
        path: "/dashboard/calendar",
        text: "Calendario",
        notification: "2 ",
        icon: <FaCalendarDays />,
      },
      {
        path: "/dashboard",
        text: "Recetas",
        notification: "2 ",
        icon: <HiDocumentCheck />,
      },
    ],
  },
  {
    subMenuText: "Configuraciones",
    subMenuItems: [
      {
        path: "/dashboard/profile",
        text: "Profile",
        notification: null,
        icon: <ImProfile />,
      },
      {
        path: "/dashboard",
        text: "Settings",
        notification: "2 ",
        icon: <IoMdSettings />,
      },
    ],
  },
];

export const appointmentsAll: RemindersResponse = {
  reminders: [
    {
      id: "1",
      eventId: "1",
      calendarId: "1",
      userId: "1",
      remindAt: new Date().toISOString(),
      deliveredAt: "2021-01-01T12:00:00Z",
      viewedAt: "2021-01-01T12:00:00Z",
      dismissedAt: "2021-01-01T12:00:00Z",
      status: ReminderStatus.PENDING,
      method: ReminderMethod.IN_APP,
      title: "Appointment",
      description: "Appointment with Dr. Smith",
    },
  ],
};
