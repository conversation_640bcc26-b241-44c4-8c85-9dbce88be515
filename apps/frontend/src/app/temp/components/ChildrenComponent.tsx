"use client";

import React, { useEffect, useState } from "react";
import { getCurrentDayInfo } from "@jocom/ui/src/components/calendar-helper";
import { Attendee, DayInfo, Events, EventsResponse } from "@jocom/types";
import { SelectDayLayout } from "@jocom/ui/src/components/layout";
import { ButtonWithImage } from "@jocom/ui/src/components/buttons";
import { SingleElementList } from "@jocom/ui/src/components/list";
import { CreateEventModal } from "@jocom/ui/src/components/modal";
import { BsCalendarEventFill } from "react-icons/bs";
import { useUserStore } from "@/stores/user-store";
import {
  getCalendarEvents,
  createCalendarEvent,
} from "@/actions/calendar-actions";

function ChildrenComponent() {
  const [selectedDay, setSelectedDay] = useState<DayInfo>(getCurrentDayInfo());
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [events, setEvents] = useState<EventsResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useUserStore();

  useEffect(() => {
    async function fetchEvents() {
      if (!user?.id || !user?.currentClinicId) {
        return;
      }

      setIsLoading(true);
      try {
        const calendarEvents = await getCalendarEvents(
          user.currentClinicId,
          user.id,
          selectedDay
        );
        setEvents(calendarEvents);
      } catch (error) {
        console.error("Failed to fetch events:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchEvents();
  }, [selectedDay, user?.id, user?.currentClinicId]);

  const handleCreateEvent = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleSubmitEvent = async (event: Events, attendees: Attendee[]) => {
    try {
      const newEvent = await createCalendarEvent(event, attendees);
      console.log("Event created successfully:", newEvent);
      setEvents((prevEvents) => [...prevEvents, newEvent]);

      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating event:", error);
    }
  };

  return (
    <>
      <SelectDayLayout
        setSelectedDay={setSelectedDay}
        selectedDay={selectedDay}
        isLoading={isLoading}
      >
        <div className="flex justify-center my-4">
          <ButtonWithImage
            label={"Crear Evento"}
            handleClick={handleCreateEvent}
            icon={<BsCalendarEventFill />}
          />
        </div>
        <SingleElementList events={events} />
      </SelectDayLayout>
      <CreateEventModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmitEvent}
        userId={user?.id || ""}
        clinicId={user?.currentClinicId || ""}
        selectedDay={selectedDay}
      />
    </>
  );
}

export default ChildrenComponent;
