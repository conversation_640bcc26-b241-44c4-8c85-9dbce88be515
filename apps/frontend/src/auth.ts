import { ChuletazoAdapter } from "@jocom/config/src/adapter/chuletazo-adapter";
import NextAuth from "next-auth";
import GitHub from "next-auth/providers/github";
import Google from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";
import axiosInstance from "@jocom/config/src/axios/axios-instance";
import { AxiosResponse } from "axios";
import {
  AuthClinic,
  AuthClinicUser,
  AuthorizedUser,
  ChuletazoResponse,
} from "@jocom/types";
import { cookies } from "next/headers";

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    GitHub,
    Google,
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const response: AxiosResponse<ChuletazoResponse<AuthorizedUser>> =
            await axiosInstance.post(`/v1/auth/login`, {
              email: credentials.email,
              password: credentials.password,
            });

          console.log("\n\n\n\nresponse", response);

          const userSignedIn = response.data.data;
          const token = userSignedIn.accessToken;

          // Set the token in the cookie
          const cookieStore = await cookies();
          cookieStore.set("token", token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            path: "/",
          });

          return { ...userSignedIn };
        } catch (error) {
          console.error("Failed to login:", error);
          return null;
        }
      },
    }),
  ],
  adapter: ChuletazoAdapter(),
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async signIn({ user }) {
      if (!user.email) {
        return false;
      }
      const res = await axiosInstance.get(
        `/v1/auth/users/whitelist/${encodeURIComponent(user.email)}`
      );
      console.log("signin");

      if (res.status >= 400) return false;

      return true;
    },
    async jwt({ token, user }) {
      // Initial sign in
      console.log("\n\n\njwt");
      if (user) {
        // Fetch user clinics from your API
        try {
          const response: AxiosResponse<ChuletazoResponse<AuthClinicUser>> =
            await axiosInstance.get(
              `/v1/auth/users/clinics/${encodeURIComponent(
                user.email as string
              )}`
            );
          console.log(response.data.data);

          const clinics: AuthClinic[] = response.data.data.clinics;

          // Explicitly cast to UserClinic[] to fix type error
          token.clinics = clinics;
          token.currentClinicId = response.data.data.currentClinicId;
          token.userId = user.id;
          token.expires = new Date(
            Date.now() + 24 * 60 * 60 * 1000
          ).toISOString();
        } catch (error) {
          console.error("Failed to fetch user clinics:", error);
          token.clinics = [];
          token.currentClinicId = "";
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        // Explicitly type the assignments to fix the error
        session.user.clinics = token.clinics as AuthClinic[];
        session.user.currentClinicId = token.currentClinicId as string;
        session.user.userId = token.userId as string;
        session.user.expires = token.expires as string;
      }
      console.log("session", session);
      return session;
    },
  },
  jwt: {
    maxAge: 60 * 60 * 24 * 30,
  },
});
