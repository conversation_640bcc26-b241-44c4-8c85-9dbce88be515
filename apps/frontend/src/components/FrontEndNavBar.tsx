"use client";

import { useUserStore } from "@/stores/user-store";
import React from "react";
import SelectClinic from "./SelectClinic";
import { Navbar, ReminderMenu } from "@jocom/ui/src/components/layout";
import { appointmentsAll } from "@/app/sideBarMenu";

function FrontEndNavBar() {
  const { user, reset } = useUserStore();

  // If user is not available yet, render nothing
  if (!user) {
    return null;
  }

  const handleLogout = () => {
    console.log("Logging out, clearing user store");
    reset();
  };

  return (
    <Navbar
      navBarElement={<SelectClinic />}
      navBarElement2={<ReminderMenu reminders={appointmentsAll.reminders} />}
      onLogout={handleLogout}
      user={user}
    />
  );
}

export default FrontEndNavBar;
