"use client";
import { useUserStore } from "@/stores/user-store";
import { SelectWithState } from "@jocom/ui/src/components/input";
import React from "react";

const SelectClinic = () => {
  const { user, updateCurrentClinic } = useUserStore();

  if (!user) {
    return null;
  }

  // If user is null, try to use session data
  const clinics = user?.clinics || [];
  const currentClinicId = user?.currentClinicId || "loading";

  // Ensure we have valid data for the select
  const clinicOptions =
    clinics.length > 0
      ? clinics.map((clinic) => ({
          key: clinic.clinicId,
          value: clinic.name,
        }))
      : [{ key: "loading", value: "Loading clinics..." }];

  const changeHandler = (value: string) => {
    updateCurrentClinic(value);
  };

  return (
    <SelectWithState
      text="Clinica:"
      id="selectClinicOnHeader"
      data={clinicOptions}
      value={currentClinicId}
      changeHandler={changeHandler}
    />
  );
};

export default SelectClinic;
