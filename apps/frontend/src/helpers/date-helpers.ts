/**
 * Format a date string to a readable date format
 * @param dateString ISO date string
 * @returns Formatted date string (e.g., "Jan 1, 2023")
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format a date string to a readable time format
 * @param dateString ISO date string
 * @returns Formatted time string (e.g., "2:30 PM")
 */
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Format a date to YYYY-MM-DD format
 * @param date Date object
 * @returns Formatted date string (e.g., "2023-01-01")
 */
export const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Get relative time description (e.g., "2 hours ago", "in 3 days")
 * @param dateString ISO date string
 * @returns Relative time description
 */
export const getRelativeTimeDescription = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  if (diffDay > 0) {
    return `in ${diffDay} ${diffDay === 1 ? 'day' : 'days'}`;
  } else if (diffDay < 0) {
    return `${Math.abs(diffDay)} ${Math.abs(diffDay) === 1 ? 'day' : 'days'} ago`;
  } else if (diffHour > 0) {
    return `in ${diffHour} ${diffHour === 1 ? 'hour' : 'hours'}`;
  } else if (diffHour < 0) {
    return `${Math.abs(diffHour)} ${Math.abs(diffHour) === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffMin > 0) {
    return `in ${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'}`;
  } else if (diffMin < 0) {
    return `${Math.abs(diffMin)} ${Math.abs(diffMin) === 1 ? 'minute' : 'minutes'} ago`;
  } else {
    return 'just now';
  }
};

export const fixNumberTwoDigits = (number: number) => {
  return number < 10 ? `0${number}` : number;
};
