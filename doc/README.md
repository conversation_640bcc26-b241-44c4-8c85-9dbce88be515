# Documentación del Sistema de Autenticación

Este directorio contiene la documentación detallada del sistema de autenticación implementado en el proyecto JOCOM, utilizando NextAuth.js con un adapter personalizado.

## Diagramas de Secuencia

### 1. [Login Sequence](./auth-login-sequence.puml)
Diagrama que muestra el flujo completo de autenticación para:
- **Login con Credenciales**: Email/password contra API backend
- **Login con OAuth**: Google y GitHub providers
- Validación de whitelist
- Creación de sesiones y tokens JWT
- Almacenamiento en cookies y store local

### 2. [Logout Sequence](./auth-logout-sequence.puml)
Diagrama que detalla los diferentes tipos de logout:
- **Logout Manual**: Usuario hace click en "Salir"
- **Logout Automático**: Por token expirado
- **Logout por Cierre**: Comportamiento al cerrar navegador
- Limpieza de sesiones, cookies y store

### 3. [Token Validation Sequence](./auth-token-validation-sequence.puml)
Diagrama que explica la validación continua de tokens:
- **Middleware de Validación**: En cada request
- **Validación Server-side**: Verificación de sesiones en BD
- **Renovación Automática**: De tokens JWT próximos a expirar
- **Validación Client-side**: Con React hooks
- **Manejo de Errores**: Tokens inválidos o expirados

## Arquitectura del Sistema

### Componentes Principales

1. **NextAuth.js Core**: Manejo central de autenticación
2. **ChuletazoAdapter**: Adapter personalizado para comunicación con backend
3. **Middleware**: Protección de rutas y validación de tokens
4. **Zustand Store**: Gestión de estado del usuario en cliente
5. **Backend API**: Endpoints de autenticación y gestión de usuarios

### Flujo de Datos

```
Usuario → Frontend → NextAuth → ChuletazoAdapter → Backend API → Database
                ↓
        Cookies + JWT Token
                ↓
        Zustand Store (localStorage)
```

### Tipos de Autenticación Soportados

1. **Credentials Provider**: Email/password
2. **Google OAuth**: Login con cuenta Google
3. **GitHub OAuth**: Login con cuenta GitHub

### Gestión de Sesiones

- **JWT Strategy**: Tokens firmados almacenados en cookies httpOnly
- **Session Persistence**: 30 días de duración máxima
- **Database Sessions**: Respaldo en BD para validación server-side
- **Automatic Cleanup**: Limpieza automática de sesiones expiradas

### Seguridad Implementada

- **httpOnly Cookies**: Previene acceso desde JavaScript
- **CSRF Protection**: Tokens CSRF automáticos
- **Secure Cookies**: En producción (HTTPS)
- **SameSite Policy**: Protección contra ataques CSRF
- **Security Headers**: X-Frame-Options, CSP, etc.
- **Whitelist Validation**: Solo usuarios autorizados pueden acceder

## Configuración de Entorno

### Variables Requeridas

```env
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret
```

### Endpoints del Backend

- `POST /v1/auth/login` - Login con credenciales
- `GET /v1/auth/users/whitelist/{email}` - Validación de whitelist
- `GET /v1/auth/users/clinics/{email}` - Datos de clínicas del usuario
- `POST /v1/auth/users` - Crear usuario
- `GET /v1/auth/users/{id}` - Obtener usuario
- `POST /v1/auth/sessions` - Crear sesión
- `GET /v1/auth/sessions/{token}` - Validar sesión
- `DELETE /v1/auth/sessions/{token}` - Eliminar sesión

## Uso de los Diagramas

Para visualizar los diagramas PlantUML:

1. **Online**: Copiar contenido a [PlantUML Online Server](http://www.plantuml.com/plantuml/uml/)
2. **VS Code**: Instalar extensión "PlantUML"
3. **CLI**: Usar plantuml.jar para generar imágenes

```bash
# Generar PNG desde CLI
java -jar plantuml.jar auth-login-sequence.puml
```

## Consideraciones de Desarrollo

### Debugging
- Logs detallados en callbacks de NextAuth
- Console.log en adapter para tracking de operaciones
- Middleware logging para requests protegidos

### Testing
- Verificar flujos de login/logout
- Validar expiración de tokens
- Probar manejo de errores de red
- Confirmar limpieza de sesiones

### Mantenimiento
- Monitorear sesiones activas en BD
- Limpiar sesiones expiradas periódicamente
- Actualizar secrets de OAuth regularmente
- Revisar logs de autenticación fallida
