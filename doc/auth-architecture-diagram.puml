@startuml Authentication Architecture
!theme plain
title Sistema de Autenticación - Arquitectura General

package "Frontend (Next.js)" {
  [Login Page] as LP
  [Protected Pages] as PP
  [Middleware] as MW
  [SessionProvider] as SP
  [Zustand Store] as ZS
  [Auth Components] as AC
}

package "NextAuth.js" {
  [NextAuth Core] as NAC
  [JW<PERSON> Handler] as JW<PERSON>
  [Session Manager] as SM
  [Callbacks] as CB
}

package "Providers" {
  [Credentials Provider] as CP
  [Google OAuth] as GO
  [GitHub OAuth] as GH
}

package "Custom Adapter" {
  [ChuletazoAdapter] as CA
}

package "Backend API" {
  [Auth Endpoints] as AE
  [User Management] as UM
  [Session Management] as SEM
  [Whitelist Validation] as WV
}

package "Database" {
  [Users Table] as UT
  [Sessions Table] as ST
  [Accounts Table] as AT
  [Verification Tokens] as VT
}

package "External Services" {
  [Google OAuth Server] as GOS
  [GitHub OAuth Server] as GHS
}

package "Browser Storage" {
  [HTTP-Only Cookies] as HC
  [Local Storage] as LS
}

' Connections
LP --> NAC : signIn()
PP --> MW : Request Intercepted
MW --> JWT : Validate Token
MW --> PP : Allow/Redirect

NAC --> CP : Credentials Auth
NAC --> GO : Google Auth
NAC --> GH : GitHub Auth

CP --> AE : POST /v1/auth/login
GO --> GOS : OAuth Flow
GH --> GHS : OAuth Flow

NAC --> CA : Adapter Operations
CA --> AE : API Calls
CA --> UM : User Operations
CA --> SEM : Session Operations

AE --> UT : User Queries
SEM --> ST : Session Queries
UM --> AT : Account Linking
NAC --> VT : Token Verification

NAC --> CB : Callbacks Execution
CB --> WV : Whitelist Check
CB --> UM : Fetch User Clinics

NAC --> SM : Session Creation
SM --> HC : Store JWT Cookies
SP --> ZS : Update Store
ZS --> LS : Persist Data

' Notes
note right of MW
  Protege todas las rutas
  excepto públicas:
  - /login
  - /api/auth/*
  - archivos estáticos
end note

note right of JWT
  Tokens JWT con:
  - userId
  - clinics[]
  - currentClinicId
  - expires (24h)
end note

note right of HC
  Cookies seguras:
  - httpOnly: true
  - secure: production
  - sameSite: "lax"
end note

note right of CA
  Adapter personalizado
  maneja todas las operaciones
  de BD a través del API
end note

note bottom of CB
  Callbacks ejecutados:
  1. signIn - Whitelist validation
  2. jwt - User clinics fetch
  3. session - Data enrichment
end note

@enduml
