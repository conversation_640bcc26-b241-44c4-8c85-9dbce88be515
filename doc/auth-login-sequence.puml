@startuml Login Sequence Diagram
!theme plain
title <PERSON>stema de Autenticación - <PERSON><PERSON><PERSON> de <PERSON>

actor <PERSON><PERSON><PERSON> as U
participant "Frontend\n(Next.js)" as FE
participant "NextAuth\nMiddleware" as MW
participant "NextAuth\nCore" as NA
participant "Credentials\nProvider" as CP
participant "OAuth Provider\n(Google/GitHub)" as OP
participant "ChuletazoAdapter" as CA
participant "Backend API\n(/v1/auth)" as API
participant "Database" as DB
participant "Browser\nCookies" as BC
participant "Zustand Store\n(user-store)" as ZS

== Login con Credenciales ==

U -> FE: Accede a página protegida
FE -> MW: Request interceptado
MW -> MW: Verifica token JWT
note right: getToken() de next-auth/jwt
MW -> FE: Redirige a /login (sin token)
FE -> U: Muestra formulario login

U -> FE: Envía email/password
FE -> NA: signIn("credentials", {email, password})
NA -> CP: authorize(credentials)

CP -> CP: Valida credentials?.email && credentials?.password
CP -> API: POST /v1/auth/login\n{email, password}
API -> DB: Verifica credenciales
DB -> API: Usuario válido + token
API -> CP: Response {data: {token}}

CP -> BC: Set cookie "token"\n{httpOnly: true, secure, sameSite: "lax"}
CP -> NA: Return {id: "1", name: "John Doe", email}

NA -> NA: signIn callback ejecutado
NA -> API: GET /v1/auth/users/whitelist/{email}
API -> DB: Verifica usuario en whitelist
DB -> API: Usuario autorizado
API -> NA: Status 200

NA -> NA: jwt callback ejecutado
NA -> API: GET /v1/auth/users/clinics/{email}
API -> DB: Obtiene clínicas del usuario
DB -> API: {clinics: [], currentClinicId}
API -> NA: AuthClinicUser data

NA -> NA: Crea JWT token con:\n- clinics\n- currentClinicId\n- userId\n- expires (24h)

NA -> CA: createSession(session)
CA -> API: POST /v1/auth/sessions
API -> DB: Guarda sesión
DB -> API: Sesión creada
API -> CA: AdapterSession
CA -> NA: Session creada

NA -> NA: session callback ejecutado
NA -> NA: Agrega a session.user:\n- clinics\n- currentClinicId\n- userId\n- expires

NA -> FE: Login exitoso + session
FE -> ZS: SessionProvider actualiza store
ZS -> ZS: Persiste usuario en localStorage
FE -> U: Redirige a página solicitada

== Login con OAuth (Google/GitHub) ==

U -> FE: Click "Login con Google/GitHub"
FE -> NA: signIn("google"/"github")
NA -> OP: Redirige a OAuth provider
OP -> U: Muestra pantalla autorización
U -> OP: Autoriza aplicación
OP -> NA: Callback con authorization code
NA -> OP: Intercambia code por access token
OP -> NA: Access token + user info

NA -> CA: getUserByAccount(provider, providerAccountId)
CA -> API: GET /v1/auth/accounts/{provider}/{providerAccountId}
API -> DB: Busca cuenta vinculada
DB -> API: Usuario existente o null

alt Usuario no existe
    NA -> CA: createUser(user)
    CA -> API: POST /v1/auth/users
    API -> DB: Crea nuevo usuario
    DB -> API: Usuario creado
    API -> CA: AdapterUser
    
    NA -> CA: linkAccount(account)
    CA -> API: POST /v1/auth/accounts
    API -> DB: Vincula cuenta OAuth
    DB -> API: Cuenta vinculada
end

NA -> NA: signIn callback (igual que credenciales)
NA -> NA: jwt callback (igual que credenciales)
NA -> CA: createSession (igual que credenciales)
NA -> NA: session callback (igual que credenciales)
NA -> FE: Login exitoso + session
FE -> ZS: SessionProvider actualiza store
FE -> U: Usuario autenticado

@enduml
