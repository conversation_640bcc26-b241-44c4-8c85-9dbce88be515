@startuml Logout Sequence Diagram
!theme plain
title Sistema de Autenticación - Flujo de Logout

actor <PERSON><PERSON><PERSON> as U
participant "Frontend\n(Next.js)" as FE
participant "LogoutNavbar\nComponent" as LN
participant "NextAuth\nCore" as NA
participant "ChuletazoAdapter" as CA
participant "Backend API\n(/v1/auth)" as API
participant "Database" as DB
participant "Browser\nCookies" as BC
participant "Zustand Store\n(user-store)" as ZS

== Logout Process ==

U -> LN: Click botón "Salir"
LN -> LN: handleLogout() ejecutado

LN -> ZS: onLogout() callback
note right: Opcional - desde FrontEndNavBar
ZS -> ZS: reset() - Limpia user store
ZS -> ZS: Elimina datos de localStorage

LN -> NA: signOut() de next-auth/react
note right: Client-side signOut

NA -> NA: Obtiene session actual
NA -> NA: Extrae sessionToken del JWT

alt Session Token existe
    NA -> CA: deleteSession(sessionToken)
    CA -> API: DELETE /v1/auth/sessions/{sessionToken}
    API -> DB: Elimina sesión de BD
    DB -> API: Sesión eliminada
    API -> CA: Confirmación
    CA -> NA: Session eliminada
end

NA -> BC: Elimina cookies de NextAuth
note right: Limpia:\n- next-auth.session-token\n- next-auth.csrf-token\n- Cookies personalizadas

NA -> NA: Invalida JWT token local
NA -> FE: Logout completado

FE -> FE: Router.push() o redirect
FE -> U: Redirige a página login

== Logout Automático por Token Expirado ==

FE -> MW: Request a página protegida
MW -> MW: getToken() verifica JWT
MW -> MW: Token expirado o inválido

MW -> NA: Token inválido detectado
NA -> CA: deleteSession(sessionToken)
CA -> API: DELETE /v1/auth/sessions/{sessionToken}
API -> DB: Limpia sesión expirada
DB -> API: Confirmación

MW -> BC: Limpia cookies automáticamente
MW -> FE: Redirect a /login
FE -> U: Usuario deslogueado automáticamente

== Logout por Cierre de Navegador ==

note over U, ZS: Al cerrar navegador:
BC -> BC: Cookies httpOnly persisten\n(hasta expiración)
ZS -> ZS: localStorage persiste\n(hasta reset manual)

note over U, ZS: Al reabrir navegador:
FE -> MW: Nueva request
MW -> MW: Verifica token en cookies
alt Token válido y no expirado
    MW -> FE: Permite acceso
    FE -> ZS: SessionProvider restaura store
else Token inválido/expirado
    MW -> FE: Redirect a login
    ZS -> ZS: Store se resetea en próximo login
end

@enduml
