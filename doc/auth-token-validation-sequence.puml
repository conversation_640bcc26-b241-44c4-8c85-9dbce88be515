@startuml Token Validation Sequence Diagram
!theme plain
title Sistema de Autenticación - Validación de Tokens y Sesiones

actor <PERSON><PERSON><PERSON> as U
participant "Frontend\n(Next.js)" as FE
participant "NextAuth\nMiddleware" as MW
participant "NextAuth\nCore" as NA
participant "ChuletazoAdapter" as CA
participant "Backend API\n(/v1/auth)" as API
participant "Database" as DB
participant "Browser\nCookies" as BC
participant "Zustand Store\n(user-store)" as ZS

== Validación en cada Request ==

U -> FE: Navega a página protegida
FE -> MW: Request interceptado por middleware

MW -> MW: Verifica si es ruta pública
note right: publicPaths: ['/login', '/api/auth/*', etc.]

alt Ruta pública o archivo estático
    MW -> FE: NextResponse.next() - Permite acceso
else Ruta protegida
    MW -> BC: getToken() extrae JWT de cookies
    note right: Cookie: next-auth.session-token
    
    MW -> MW: Verifica token JWT
    note right: Valida:\n- Firma JWT\n- Expiración\n- Estructura
    
    alt Token válido
        MW -> MW: Agrega security headers
        note right: X-Frame-Options: DENY\nX-Content-Type-Options: nosniff\nReferrer-Policy: strict-origin-when-cross-origin\nContent-Security-Policy: default-src 'self'
        MW -> FE: NextResponse.next() - Permite acceso
    else Token inválido/expirado/ausente
        MW -> FE: Redirect a /login?callbackUrl={currentUrl}
    end
end

== Validación de Sesión en Servidor ==

FE -> NA: auth() - Server-side session check
NA -> BC: Extrae sessionToken de cookies
NA -> CA: getSessionAndUser(sessionToken)
CA -> API: GET /v1/auth/sessions/{sessionToken}
API -> DB: Busca sesión activa
DB -> API: {session, user} o null

alt Sesión válida en BD
    API -> CA: {session: AdapterSession, user: AdapterUser}
    CA -> NA: Session data
    NA -> NA: Verifica expiración de sesión
    
    alt Sesión no expirada
        NA -> FE: Session válida
    else Sesión expirada
        NA -> CA: deleteSession(sessionToken)
        CA -> API: DELETE /v1/auth/sessions/{sessionToken}
        API -> DB: Elimina sesión expirada
        NA -> FE: null (sin sesión)
    end
else Sesión no existe en BD
    API -> CA: null
    CA -> NA: null
    NA -> FE: null (sin sesión)
end

== Renovación Automática de Token JWT ==

FE -> NA: Request con token próximo a expirar
NA -> NA: jwt() callback ejecutado
NA -> NA: Verifica si token.expires < now + threshold

alt Token necesita renovación
    NA -> API: GET /v1/auth/users/clinics/{email}
    note right: Refresca datos de clínicas
    API -> DB: Datos actualizados del usuario
    DB -> API: {clinics, currentClinicId}
    API -> NA: Datos frescos
    
    NA -> NA: Actualiza token JWT:
    note right: - Nuevos datos de clínicas\n- Nueva fecha de expiración\n- Mantiene userId
    
    NA -> BC: Actualiza cookie con nuevo JWT
else Token aún válido
    NA -> NA: Mantiene token actual
end

== Validación en Cliente (React) ==

FE -> ZS: useSession() hook
ZS -> NA: Verifica session del cliente
NA -> BC: Lee JWT de cookies
NA -> NA: Valida token localmente

alt Token válido
    NA -> ZS: {data: session, status: "authenticated"}
    ZS -> ZS: SessionProvider actualiza store
    ZS -> ZS: Persiste en localStorage
    ZS -> FE: Usuario autenticado
else Token inválido
    NA -> ZS: {data: null, status: "unauthenticated"}
    ZS -> ZS: reset() - Limpia store
    ZS -> FE: Usuario no autenticado
end

== Manejo de Errores de Autenticación ==

API -> CA: Error 401/403 en cualquier endpoint
CA -> NA: Error de autenticación
NA -> BC: Limpia cookies automáticamente
NA -> ZS: Trigger logout
ZS -> ZS: reset() store
ZS -> FE: Redirect a login

== Validación de Whitelist ==

note over NA, API: Durante signIn callback:
NA -> API: GET /v1/auth/users/whitelist/{email}
API -> DB: Verifica email en whitelist
DB -> API: Usuario autorizado/no autorizado

alt Usuario en whitelist
    API -> NA: Status 200
    NA -> NA: Continúa proceso de login
else Usuario no autorizado
    API -> NA: Status 400+
    NA -> NA: signIn callback return false
    NA -> FE: Login rechazado
end

@enduml
