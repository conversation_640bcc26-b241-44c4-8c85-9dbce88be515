{"name": "jocom", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"clean": "rm -rf node_modules/.cache && rm -rf apps/*/.next && rm -rf apps/*/dist && rm -rf packages/*/dist && rm -rf .turbo", "clean:all": "npm run clean && rm -rf node_modules && rm -rf **/node_modules", "dev": "turbo run dev", "dev:clean": "npm run clean && turbo run dev", "build": "turbo run build", "build:clean": "npm run clean && turbo run build", "lint": "turbo run lint", "dev:admin": "turbo run dev --filter=admin", "dev:frontend": "turbo run dev --filter=frontend", "build:admin": "turbo run build --filter=admin", "build:frontend": "turbo run build --filter=frontend", "dev:types": "turbo run dev --filter=@jocom/types", "dev:config": "turbo run dev --filter=@jocom/config", "dev:ui": "turbo run dev --filter=@jocom/ui", "build:types": "turbo run build --filter=@jocom/types"}, "keywords": [], "workspaces": ["apps/*", "packages/*"], "author": "", "license": "ISC", "description": "", "devDependencies": {"postcss": "^8", "tailwindcss": "^3.4.1", "turbo": "^2.5.3"}, "packageManager": "npm@10.8.3", "dependencies": {"react-icons": "^5.5.0"}}