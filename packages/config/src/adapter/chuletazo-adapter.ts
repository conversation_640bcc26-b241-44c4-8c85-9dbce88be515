import {
  <PERSON><PERSON>er,
  AdapterUser,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ion,
  AdapterAccount,
  VerificationToken,
} from "next-auth/adapters";

import { AxiosResponse } from "axios";
import axiosInstance from "../axios/axios-instance";

const PREFIX = "/v1/auth";

export function ChuletazoAdapter(): Adapter {
  return {
    async createUser(user): Promise<AdapterUser> {
      console.log("createuser");
      const res: AxiosResponse<AdapterUser> = await axiosInstance.post(
        `${PREFIX}/users`,
        user
      );

      res.data.emailVerified = res.data.emailVerified
        ? new Date(res.data.emailVerified)
        : null;
      return res.data;
    },

    async getUser(id: string): Promise<AdapterUser | null> {
      console.log("getuser");
      const res: AxiosResponse<AdapterUser> = await axiosInstance.get(
        `${PREFIX}/users/${id}`
      );
      if (res.status >= 400) return null;
      return res.data;
    },

    async updateUser(
      user: Partial<AdapterUser> & { id: string }
    ): Promise<AdapterUser> {
      console.log("updateuser");
      const res: AxiosResponse<AdapterUser> = await axiosInstance.put(
        `${PREFIX}/users/${user.id}`,
        user
      );
      return res.data;
    },

    async deleteUser(userId: string): Promise<void> {
      console.log("deleteuser");
      await axiosInstance.delete(`${PREFIX}/users/${userId}`);
    },

    async getUserByEmail(email: string): Promise<AdapterUser | null> {
      console.log("getuserbyemail");
      return null;
      /*const res: AxiosResponse<AdapterUser | null> = await axiosInstance.get(
        `${PREFIX}/users/email/${encodeURIComponent(email)}`
      );
      if (res.status >= 400) return null;
      return res.data;*/
    },

    async getUserByAccount({
      provider,
      providerAccountId,
    }: {
      provider: string;
      providerAccountId: string;
    }): Promise<AdapterUser | null> {
      console.log("getuserbyaccount");

      const res: AxiosResponse<AdapterUser | null> = await axiosInstance.get(
        `${PREFIX}/accounts/${provider}/${providerAccountId}`
      );

      if (res.status >= 400) return null;
      console.log(res.data);
      return res.data;
    },

    async linkAccount(account: AdapterAccount): Promise<void> {
      console.log("linkAccount");
      await axiosInstance.post(`${PREFIX}/accounts`, account);
    },

    async unlinkAccount({
      provider,
      providerAccountId,
    }: {
      provider: string;
      providerAccountId: string;
    }): Promise<void> {
      console.log("unlinkAccount");
      await axiosInstance.delete(
        `${PREFIX}/accounts/${provider}/${providerAccountId}`
      );
    },

    async createSession(session: AdapterSession): Promise<AdapterSession> {
      console.log("createSession");
      const res: AxiosResponse<AdapterSession> = await axiosInstance.post(
        `${PREFIX}/sessions`,
        session
      );
      if (res.status >= 400) {
        throw new Error("Failed to create session");
      }
      console.log(res);
      return res.data;
    },

    async getSessionAndUser(
      sessionToken: string
    ): Promise<{ session: AdapterSession; user: AdapterUser } | null> {
      console.log("getSessionAndUser");
      const res: AxiosResponse<{ session: AdapterSession; user: AdapterUser }> =
        await axiosInstance.get(`${PREFIX}/sessions/${sessionToken}`);

      if (res.status >= 400) return null;

      res.data.session.expires = new Date(res.data.session.expires);
      return res.data;
    },

    async updateSession(
      session: Partial<AdapterSession> & { sessionToken: string }
    ): Promise<AdapterSession | null> {
      console.log("updateSEssion");
      const res: AxiosResponse<AdapterSession | null> = await axiosInstance.put(
        `${PREFIX}/sessions/${session.sessionToken}`,
        session
      );

      if (res.status >= 400) return null;

      if (res.data) {
        res.data.expires = new Date(res.data.expires);
      }

      return res.data;
    },

    async deleteSession(sessionToken: string): Promise<void> {
      console.log("deleteSession");
      await axiosInstance.delete(`${PREFIX}/sessions/${sessionToken}`);
    },

    async createVerificationToken(
      token: VerificationToken
    ): Promise<VerificationToken> {
      console.log("create verification token");

      const res: AxiosResponse<VerificationToken> = await axiosInstance.post(
        `${PREFIX}/verification-tokens`,
        token
      );
      res.data.expires = new Date(res.data.expires);

      return res.data;
    },

    async useVerificationToken(params: {
      identifier: string;
      token: string;
    }): Promise<VerificationToken | null> {
      console.log("useVerificationToken");
      const res: AxiosResponse<VerificationToken | null> =
        await axiosInstance.post(`${PREFIX}/verification-tokens/use`, params);

      if (res.status >= 400) return null;
      if (res.data) {
        res.data.expires = new Date(res.data?.expires);
      }
      return res.data;
    },
  };
}
