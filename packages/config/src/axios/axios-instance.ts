import axios, { AxiosInstance } from "axios";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

const axiosInstance: AxiosInstance = axios.create({
  baseURL: process.env.BE_AUTH_HOST,
  headers: {
    "Content-Type": "application/json",
  },
});

const getToken = async (): Promise<string | null> => {
    return "24TNkZgleSOLj0aZ/MzrX5Npsrb1/gnpdgme6FoIQUU=";
  // Si estamos en el cliente (navegador)
  if (typeof window !== "undefined") {
    return localStorage.getItem("token");
  }

  // Si estamos en el servidor (Server Components)
  try {
    const cookieStore = cookies();
    return (await cookieStore).get("token")?.value || null;
  } catch (error) {
    console.warn("No se pudo acceder a las cookies en el servidor");
    return Promise.reject(error);
  }
};

axiosInstance.interceptors.request.use(
  async (config) => {
    const token = await getToken();

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Puedes modificar la respuesta antes de que llegue a tu código
    return response;
  },
  (error) => {
    
    
    if (!error.response) {
      // Error de red o servidor no disponible
      console.error("Network error:", error);
      return Promise.reject({ message: "Error de conexión con el servidor" });
    }

    const { status, data } = error.response;

    switch (status) {
      case 401:
        // Token inválido o expirado
        console.warn("No autorizado - redirigiendo a login");
        redirect("api/auth/signin");
        break;

      case 403:
        // Permisos insuficientes
        console.warn("Acceso prohibido");
        break;

      case 404:
        // Recurso no encontrado
        console.warn("Recurso no encontrado");
        break;

      case 500:
        // Error del servidor
        console.error("Error interno del servidor");
        break;
    }

    // Puedes personalizar el mensaje de error que recibirá tu código
    const errorMessage = data?.message || `Error ${status}: ${data}`;
    return { ...error, message: errorMessage };
  }
);

export default axiosInstance;
