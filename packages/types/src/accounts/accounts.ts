export interface ListAccounts {
  accounts: AccountResume[];
}

export interface AccountResume {
  id: string;
  name: string|null;
  email: string;
  image: string|null;
  state: string;
  billState: string| null;
  clinics: ClinicResume[];
}

export interface ClinicResume {
  name: string;
  clinicId: string;
  products?: ProductResume[];
}

export interface ProductResume {
  productId: string;
  name: string;
}

/**
 * Action getAccountResume by email
 */

export interface SingleAccountResume {
  id: string;
  name: string;
  email: string;
  photoUrl: string;
  state: string;
  billState: string;
  clinics: SingleClinicResume[];
}

export interface SingleClinicResume {
  clinicId: string;
  name: string;
  users: UserResume[];
  products: SingleProductResume[];
}

export interface SingleProductResume {
  productId: string;
  name: string;
}

export interface UserResume {
  idUser?: string;
  name: string;
  photoUrl: string;
  email: string;
  rol: string;
  prefix: string;
}


/** NEW ACCOUNT ACTIONS */
export interface NewAccountData{
  name: string, 
  email: string, 
  idProduct: string,
  clinicName: string,
  idAccount?: string,
  idClinic?: string,
}