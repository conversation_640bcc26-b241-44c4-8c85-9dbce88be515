import { EventStatus } from "@jocom/ui/src/components/constants";


export interface Calendar {
  id: string;
  clinicId: string;  // ahora el calendario pertenece a la clínica
  name: string;
  color: string;
  isDefault: boolean;
}

export interface Events {
  id: string;
  calendarId: string;
  title: string;
  description?: string;
  startTime: string; // ISO string
  endTime: string;   // ISO string
  isAllDay: boolean;
  location?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string; // userId que creó el evento
}

export interface Attendee {
  eventId: string;
  userId: string;
  status: EventStatus
}

export interface RecurrenceRule {
  id: string;
  eventId: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number;
  byDay?: string[]; // e.g., ['MO', 'WE', 'FR']
  until?: string;   // ISO string
}
  

//response 
export interface EventsResponse extends Events {
  attendees: AttendeeResponse[];
}

export interface AttendeeResponse extends Attendee {
    photoUrl: string;
    name: string;
    email: string;
    status: EventStatus;
}

export interface AttendeeUpdateStatus {
  userId: string;
  status: EventStatus
}