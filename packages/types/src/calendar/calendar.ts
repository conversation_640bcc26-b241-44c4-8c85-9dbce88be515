import { DayWeek, DayWeekMin } from "@jocom/ui/src/components/constants";


export interface Day{
    month: number;
    day: number;
    year: number;
}

export interface DayInfo extends Day{
    dayOfWeek: number;
}

export interface SevenDaysInfo{
    days: DayInfo[];
}

export interface WeekInfo{
    startWeekDay: DayInfo;
    endWeekDay: DayInfo;
}

export interface MonthInfo{
    month: number;
    year: number;
    numberOfDays: number;
    weeks: WeekInfo[];
    
}
