import { Reminder<PERSON>eth<PERSON>, ReminderStatus } from "@jocom/ui/src/components/constants";


export interface EventReminder {
  id: string;
  eventId: string;
  calendarId: string;
  userId: string;
  remindAt: string;     // date ISO string
  deliveredAt?: string; // date ISO string
  viewedAt?: string;  // date ISO string
  dismissedAt?: string;  // date ISO string
  status: ReminderStatus;
  method: ReminderMethod;
  title?: string;
  description?: string; 
}

//responses 


export interface RemindersResponse {
  reminders: EventReminder[];
}


export interface ReminderResponse {
  reminder: EventReminder;
  message: string; // Ej: "Reminder marked as viewed"
}


export interface DeleteReminderResponse {
  reminderId: string;
  message: string; // Ej: "Reminder deleted"
}

