//use for auth.js

export interface AuthUser {
  id: string
  name?: string | null
  email: string 
  image?: string | null
  emailVerified: Date | null
  clinics?: UserClinic[] | null;
  currentClinicId?: string | null;
}

export interface StoredUser extends AuthUser {
  clinics: UserClinic[] ;
  currentClinicId: string ;
}

export interface UserClinic {
  name: string;
  clinicId: string;
}

//create New User in a clinic

export interface NewUserClinicData {
  name: string;
  email: string;
  rol: string;
  clinicId: string;
}

export interface AuthClinicUser{
  clinics: AuthClinic[];
  currentClinicId: string;
}

export interface AuthClinic{
  name: string;
  clinicID: string;
  rol: string;
} 


export interface AuthorizedUser {
  id: string;
  name: string;
  email: string;
  image: string;
  accessToken: string;
} 
