{"name": "@jocom/ui", "version": "1.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"clean": "rm -rf dist", "build": "npm run clean && tsc", "dev": "npm run clean && tsc --watch"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3"}, "dependencies": {"@jocom/types": "workspace:*", "postcss": "^8", "react-icons": "^5.5.0", "tailwindcss": "^3.4.1"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}