import React, { JSX } from "react";

interface Props {
  label: string;
  handleClick: () => void;
  icon: JSX.Element;
}

export const ButtonWithImage = ({ label, handleClick, icon }: Props) => {
  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 dark:ring-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 dark:text-white"
        aria-expanded="false"
        aria-haspopup="true"
        onClick={handleClick}
      >
        {icon}
        {label}
      </button>
    </div>
  );
};
