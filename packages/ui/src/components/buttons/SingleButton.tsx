import React from "react";

interface Props {
  label: string;
  handleClick: () => void;
}

export const SingleButton = ({ label, handleClick }: Props) => {
  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center gap-x-1.5 rounded-md bg-blue-600 text-white hover:bg-blue-800 dark:bg-gray-600 px-3 py-2 text-sm font-semibold  shadow-xs ring-1 ring-inset ring-gray-300 dark:ring-gray-800  dark:hover:bg-gray-800 "
        aria-expanded="false"
        aria-haspopup="true"
        onClick={handleClick}
      >
        {label}
      </button>
    </div>
  );
};
