import { Day, DayInfo, SevenDaysInfo, WeekInfo, MonthInfo } from "@jocom/types";

export function getCurrentDay(): Day {
  const currentDate = new Date();
  return {
    month: currentDate.getMonth() + 1,
    day: currentDate.getDate(),
    year: currentDate.getFullYear(),
  };
}

export function getCurrentDayInfo(): DayInfo {
  const currentDate = new Date();
  return {
    month: currentDate.getMonth() + 1,
    day: currentDate.getDate(),
    year: currentDate.getFullYear(),
    dayOfWeek: currentDate.getDay(),
  };
}

export function getDayInfo(day: Day): DayInfo {
  const { month, day: dayOfMonth, year } = day;
  
  // JavaScript Date months are 0-indexed (0-11), so we subtract 1 from the month
  const date = new Date(year, month - 1, dayOfMonth);
  const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
  
  return {
    ...day,
    dayOfWeek
  };
}
  
export function getWeekInfoBasedOnDay(day: Day): SevenDaysInfo {
  const { month, day: dayOfMonth, year } = day;
  
  // Create Date object for the given day
  const centerDate = new Date(year, month - 1, dayOfMonth);
  
  // Array to store the 7 days (3 before, the center day, 3 after)
  const sevenDays: DayInfo[] = [];
  
  // Add 3 days before
  for (let i = -3; i <= 3; i++) {
    // Clone the center date and add/subtract days
    const currentDate = new Date(centerDate);
    currentDate.setDate(centerDate.getDate() + i);
    
    // Create Day object from the current date
    const currentDay: Day = {
      month: currentDate.getMonth() + 1, // Convert back to 1-indexed month
      day: currentDate.getDate(),
      year: currentDate.getFullYear()
    };
    
    // Convert to DayInfo using the existing function and add to array
    sevenDays.push(getDayInfo(currentDay));
  }
  
  return { days: sevenDays };
}

/**
 * Helper function to get the number of days in a month
 */
function getDaysInMonth(month: number, year: number): number {
  // Month is 1-indexed in our interface but 0-indexed in Date
  return new Date(year, month, 0).getDate();
}

/**
 * Helper function to get the first day of a month as a DayInfo object

function getFirstDayOfMonth(month: number, year: number): DayInfo {
  return getDayInfo({
    month,
    day: 1,
    year
  });
} */

/**
 * Helper function to get the last day of a month as a DayInfo object
 
function getLastDayOfMonth(month: number, year: number): DayInfo {
  const daysInMonth = getDaysInMonth(month, year);
  return getDayInfo({
    month,
    day: daysInMonth,
    year
  });
}*/

/**
 * Generate week information for a month
 */
function generateWeeks(month: number, year: number): WeekInfo[] {
  const daysInMonth = getDaysInMonth(month, year);
  const weeks: WeekInfo[] = [];
  
  // Start with the first day of the month
  let currentDay = 1;
  
  while (currentDay <= daysInMonth) {
    // Get the day info for the current day
    const startDay: Day = { month, day: currentDay, year };
    const startDayInfo = getDayInfo(startDay);
    
    // Calculate the end of the week (either Saturday or the last day of the month)
    let endDayNumber = currentDay;
    
    // Get JavaScript's day of week (0-6, where 0 is Sunday)
    const date = new Date(year, month - 1, currentDay);
    const dayOfWeek = date.getDay();
    
    // Calculate days until Saturday (6 is Saturday in JavaScript's 0-6 system)
    // If today is Sunday (0), we need 6 days to reach Saturday
    // If today is Monday (1), we need 5 days to reach Saturday
    // If today is Saturday (6), we need 0 days to reach Saturday
    const daysUntilSaturday = dayOfWeek === 6 ? 0 : 6 - dayOfWeek;
    endDayNumber = Math.min(currentDay + daysUntilSaturday, daysInMonth);
    
    // Create the end day info
    const endDay: Day = { month, day: endDayNumber, year };
    const endDayInfo = getDayInfo(endDay);
    
    // Add the week info
    weeks.push({
      startWeekDay: startDayInfo,
      endWeekDay: endDayInfo
    });
    
    // Move to the next week
    currentDay = endDayNumber + 1;
  }
  
  return weeks;
}

export function getMonthInfo(month: number, year: number): MonthInfo {
  const numberOfDays = getDaysInMonth(month, year);
  const weeks = generateWeeks(month, year);
  
  return {
    month,
    year,
    numberOfDays,
    weeks
  };
}


