import React from "react";
import CalendarDayMonthMini from "./CalendarDayMonthMini";
import CalendarDayEventList from "./CalendarDayEventList";
import { calendarDayEventList } from "./CalendarDayInterfaces";

const CalendarDay = () => {
  return (
    <div>
      {/**<!-- component --> */}
      <h2 className="text-base font-semibold leading-6 ">
        Actividades del día
      </h2>
      <div className="lg:grid lg:grid-cols-12 lg:gap-x-16">
        <CalendarDayMonthMini />

        <CalendarDayEventList
          eventPerDoctor={calendarDayEventList.eventPerDoctor}
        />
      </div>
    </div>
  );
};

export default CalendarDay;
