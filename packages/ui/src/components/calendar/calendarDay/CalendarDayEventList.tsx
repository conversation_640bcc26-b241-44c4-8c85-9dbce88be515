import React from "react";
import CalendarDayEventListCard from "./CalendarDayEventListCard";
import { CalendarDayEventListProps } from "./CalendarDayInterfaces";

const CalendarDayEventList = ({
  eventPerDoctor,
}: CalendarDayEventListProps) => {
  return (
    <div className="mt-4 divide-y divide-gray-100 text-sm leading-6 lg:col-span-7 xl:col-span-8">
      <div className="flex justify-center p-2 ">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg w-full   p-4 shadow-sm">
          {eventPerDoctor.map((element) => (
            <CalendarDayEventListCard
              key={element.name}
              events={element.events}
              name={element.name}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default CalendarDayEventList;
