import React from "react";
import { CalendarDayEventListCardProps } from "./CalendarDayInterfaces";
import CalendarDayEventListCardItem from "./CalendarDayEventListCardItem";

const CalendarDayEventListCard = ({
  events,
  name,
}: CalendarDayEventListCardProps) => {
  return (
    <div>
      <span className="text-gray-900 dark:text-gray-100 relative inline-block date uppercase font-medium tracking-widest">
        {name}
      </span>
      {events.map((eventItem) => (
        <CalendarDayEventListCardItem key={eventItem.id} event={eventItem} />
      ))}
    </div>
  );
};

export default CalendarDayEventListCard;
