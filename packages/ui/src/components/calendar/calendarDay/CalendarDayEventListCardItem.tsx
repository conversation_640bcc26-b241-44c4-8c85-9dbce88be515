import React from "react";
import { CalendarDayEventListCardItemProps } from "./CalendarDayInterfaces";

const CalendarDayEventListCardItem = ({
  event,
}: CalendarDayEventListCardItemProps) => {
  return (
    <div className="flex mb-2">
      <div className="w-2/12">
        <span className="text-sm text-gray-800 dark:text-gray-200 block">
          {event.hourStart}
        </span>
        <span className="text-sm text-gray-800 dark:text-gray-200 block">
          {event.hourEnd}
        </span>
      </div>
      <div className="w-1/12">
        <span
          className={`${event.colorEvent} h-2 w-2 rounded-full block mt-2`}
        ></span>
      </div>
      <div className="w-9/12">
        <span className="text-sm font-semibold block">{event.label}</span>
        <span className="text-sm">{event.description}</span>
      </div>
    </div>
  );
};

export default CalendarDayEventListCardItem;
