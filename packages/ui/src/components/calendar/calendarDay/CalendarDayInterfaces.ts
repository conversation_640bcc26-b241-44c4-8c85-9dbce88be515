import {   Event } from "@jocom/types";
import { ColorEvent } from "../../constants";

export interface CalendarDayEventListCardItemProps{
    event: Event
}

export interface CalendarDayEventListCardProps{
    events: Array<Event>, 
    name: string
}

export interface CalendarDayEventListProps{
    eventPerDoctor: Array<CalendarDayEventListCardProps>
}


export const Doctor1Events:Array<Event>=[{
    label: "Core Development",
    hourStart: "10:00",
    hourEnd: "11:00",
    id: "257",
    colorEvent: ColorEvent.BLUE,
    description: "<PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>"
}, 
{
    label: "Core Development",
    hourStart: "12:00",
    hourEnd: "13:00",
    id: "258",
    colorEvent: ColorEvent.BLUE,
    description: "<PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>"
}, 
{
    label: "Core Development",
    hourStart: "15:00",
    hourEnd: "15:30",
    id: "259",
    colorEvent: ColorEvent.BLUE,
    description: "Interview with <PERSON>"
}]

export const Doctor2Events:Array<Event>=[{
    label: "Core Development",
    hourStart: "10:00",
    hourEnd: "11:00",
    id: "357",
    colorEvent: ColorEvent.PURPLE,
    description: "<PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>"
}, 
{
    label: "Core Development",
    hourStart: "12:00",
    hourEnd: "13:00",
    id: "358",
    colorEvent: ColorEvent.PURPLE,
    description: "Joey, Matt, CJ and Vlad"
}, 
{
    label: "Core Development",
    hourStart: "15:00",
    hourEnd: "15:30",
    id: "359",
    colorEvent: ColorEvent.PURPLE,
    description: "Interview with Ed Harris"
}]

export const calendarDayEventList:CalendarDayEventListProps = {
    eventPerDoctor : [{
        name: "doctor who?",
        events: Doctor1Events
    },{
        name: "Kurokame",
        events: Doctor2Events
    }]
}