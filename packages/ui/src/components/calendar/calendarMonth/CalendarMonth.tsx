import React from "react";
import CalendarMonthTable from "./CalendarMonthTable";
import { BiLeftArrowCircle, BiRightArrowCircle } from "react-icons/bi";
import { ColorEvent } from "../../constants";

export interface CalendarMonthDayCellEventProps {
  label: string;
  hourStart: string;
  hourEnd: string;
  id: string;
  colorEvent: ColorEvent;
}

export interface CalendarMonthDayCellProps {
  isOffDay: boolean;
  day: number;
  dayEvents: Array<CalendarMonthDayCellEventProps>;
}

export interface CalendarMonthWeekLineProps {
  daysOfTheWeek: Array<CalendarMonthDayCellProps>;
  weekId: string;
}

export interface CalendarMonthTableProps {
  weeksOfTheMonth: Array<CalendarMonthWeekLineProps>;
}

const month: Array<CalendarMonthWeekLineProps> = [
  {
    weekId: "week-1",
    daysOfTheWeek: [
      {
        day: 1,
        isOffDay: false,
        dayEvents: [
          {
            label: "Meeting",
            hourStart: "12:00",
            hourEnd: "14:00",
            id: "177",
            colorEvent: ColorEvent.BLUE,
          },
          {
            label: "Paciente 2",
            hourStart: "16:00",
            hourEnd: "18:00",
            id: "178",
            colorEvent: ColorEvent.PURPLE,
          },
        ],
      },
      {
        day: 2,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 3,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 4,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 5,
        isOffDay: false,
        dayEvents: [
          {
            label: "Paciente 2",
            hourStart: "16:00",
            hourEnd: "18:00",
            id: "178",
            colorEvent: ColorEvent.PURPLE,
          },
        ],
      },
      {
        day: 6,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 7,
        isOffDay: false,
        dayEvents: [
          {
            label: "Meeting",
            hourStart: "12:00",
            hourEnd: "14:00",
            id: "177",
            colorEvent: ColorEvent.BLUE,
          },
        ],
      },
    ],
  },

  {
    weekId: "week-2",
    daysOfTheWeek: [
      {
        day: 8,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 9,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 10,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 11,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 12,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 13,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 14,
        isOffDay: false,
        dayEvents: [],
      },
    ],
  },

  {
    weekId: "week-3",
    daysOfTheWeek: [
      {
        day: 15,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 16,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 17,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 18,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 19,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 20,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 21,
        isOffDay: false,
        dayEvents: [],
      },
    ],
  },

  {
    weekId: "week-4",
    daysOfTheWeek: [
      {
        day: 22,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 23,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 24,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 25,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 26,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 27,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 28,
        isOffDay: false,
        dayEvents: [],
      },
    ],
  },

  {
    weekId: "week-5",
    daysOfTheWeek: [
      {
        day: 29,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 30,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 31,
        isOffDay: false,
        dayEvents: [],
      },
      {
        day: 1,
        isOffDay: true,
        dayEvents: [],
      },
      {
        day: 2,
        isOffDay: true,
        dayEvents: [],
      },
      {
        day: 3,
        isOffDay: true,
        dayEvents: [],
      },
      {
        day: 4,
        isOffDay: true,
        dayEvents: [],
      },
    ],
  },
];

const CalendarMonth = () => {
  return (
    <div className="wrapper  rounded-sm shadow-sm w-full ">
      <div className="header flex justify-between border-b p-2">
        <span className="text-lg font-bold">2020 July</span>
        <div className="buttons">
          <button className="p-1">
            <BiLeftArrowCircle className="h-6 w-6" />
          </button>
          <button className="p-1">
            <BiRightArrowCircle className="h-6 w-6" />
          </button>
        </div>
      </div>

      <CalendarMonthTable weeksOfTheMonth={month} />
    </div>
  );
};

export default CalendarMonth;
