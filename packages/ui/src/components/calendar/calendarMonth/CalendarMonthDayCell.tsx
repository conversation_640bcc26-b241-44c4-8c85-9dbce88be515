import React from "react";
import CalendarMonthDayCellEvent from "./CalendarMonthDayCellEvent";
import { CalendarMonthDayCellProps } from "./CalendarMonth";

const CalendarMonthDayCell = ({
  day,
  dayEvents,
  isOffDay,
}: CalendarMonthDayCellProps) => {
  const colorDayOff = "bg-gray-100 dark:bg-gray-900";
  return (
    <td
      className={`border ${
        isOffDay ? colorDayOff : ""
      } p-1 h-40 xl:w-40 lg:w-35 md:w-25 sm:w-15 w-10 overflow-auto transition cursor-pointer duration-500 ease hover:bg-gray-300 dark:hover:bg-gray-800`}
    >
      <div className="flex flex-col h-40 mx-auto xl:w-40 lg:w-35 md:w-25 sm:w-full w-10 overflow-hidden">
        <div className="top h-5 w-full">
          <span className="text-gray-500 dark:text-gray-50">{day}</span>
        </div>
        <ul className=" list-none bottom grow h-30 py-1  w-full cursor-pointer">
          {dayEvents.map((dayEvent) => (
            <CalendarMonthDayCellEvent
              key={dayEvent.id}
              id={dayEvent.id}
              label={dayEvent.label}
              hourStart={dayEvent.hourStart}
              hourEnd={dayEvent.hourEnd}
              colorEvent={dayEvent.colorEvent}
            />
          ))}
        </ul>
      </div>
    </td>
  );
};

export default CalendarMonthDayCell;
