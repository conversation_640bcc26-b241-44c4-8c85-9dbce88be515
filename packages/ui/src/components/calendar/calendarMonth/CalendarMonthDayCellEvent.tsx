import React from "react";
import { CalendarMonthDayCellEventProps } from "./CalendarMonth";

const CalendarMonthDayCellEvent = ({
  label,
  hourEnd,
  hourStart,
  id,
  colorEvent,
}: CalendarMonthDayCellEventProps) => {
  return (
    <li
      key={id}
      className={`event ${colorEvent}  rounded-sm p-1 text-sm mx-0 px-0 mb-1`}
    >
      <span className="event-name">{label} </span>
      <span className="time">
        ({hourStart}~{hourEnd})
      </span>
    </li>
  );
};

export default CalendarMonthDayCellEvent;
