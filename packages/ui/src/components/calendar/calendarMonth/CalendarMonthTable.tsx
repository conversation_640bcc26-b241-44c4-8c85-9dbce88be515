import React from "react";
import { CalendarMonthTableProps } from "./CalendarMonth";
import CalendarMonthWeekLine from "./CalendarMonthWeekLine";
import { DayWeek, DayWeekMin } from "../../constants";

const CalendarMonthTable = ({ weeksOfTheMonth }: CalendarMonthTableProps) => {
  return (
    <table className="w-full">
      <thead>
        <tr>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.SUNDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.SUNDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.MONDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.MONDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.TUESDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.TUESDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.WEDNESDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.WEDNESDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.THURSDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.THURSDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.FRIDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.FRIDAY}
            </span>
          </th>
          <th className="p-2 border-r h-10 xl:w-40 lg:w-30 md:w-30 sm:w-20 w-10 xl:text-sm text-xs">
            <span className="xl:block lg:block md:block sm:block hidden">
              {DayWeek.SATURDAY}
            </span>
            <span className="xl:hidden lg:hidden md:hidden sm:hidden block">
              {DayWeekMin.SATURDAY}
            </span>
          </th>
        </tr>
      </thead>
      <tbody>
        {weeksOfTheMonth.map((week) => (
          <CalendarMonthWeekLine
            daysOfTheWeek={week.daysOfTheWeek}
            key={week.weekId}
            weekId={week.weekId}
          />
        ))}
      </tbody>
    </table>
  );
};

export default CalendarMonthTable;
