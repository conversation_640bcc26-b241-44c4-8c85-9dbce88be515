import React from "react";
import CalendarMonthDayCell from "./CalendarMonthDayCell";
import { CalendarMonthWeekLineProps } from "./CalendarMonth";

const CalendarMonthWeekLine = ({
  daysOfTheWeek,
}: CalendarMonthWeekLineProps) => {
  return (
    <tr className="text-center h-20">
      {daysOfTheWeek.map((dayOfTheWeek) => (
        <CalendarMonthDayCell
          key={dayOfTheWeek.day}
          day={dayOfTheWeek.day}
          isOffDay={dayOfTheWeek.isOffDay}
          dayEvents={dayOfTheWeek.dayEvents}
        />
      ))}
    </tr>
  );
};

export default CalendarMonthWeekLine;
