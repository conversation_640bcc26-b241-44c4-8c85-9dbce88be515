"use client";

import { useEffect, useState } from "react";
import { WeekResumeItem } from "./WeekResumeItem";
import { DayWeekMinMap } from "../../constants";
import { DayInfo, SevenDaysInfo } from "@jocom/types";

interface props {
  weekInfo: SevenDaysInfo;
  daySelected: number;
  onClick: (day: DayInfo) => void;
}

export const WeekResume = ({ weekInfo, daySelected, onClick }: props) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 640);
      };

      // Initial check
      checkMobile();

      // Add event listener for resize
      window.addEventListener("resize", checkMobile);

      // Cleanup
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  // For mobile, show only 5 days (2 before selected, selected, 2 after selected)
  const displayDays = isMobile
    ? getReducedDays(weekInfo.days, daySelected)
    : weekInfo.days;

  return (
    <div className="flex bg-gray-200 dark:bg-gray-600 dark:text-white shadow-md justify-start md:justify-center rounded-lg overflow-x-scroll md:overflow-x-hidden mx-auto py-4 px-2 md:mx-12">
      {displayDays.map((day) => (
        <WeekResumeItem
          day={day}
          dayWeekMin={DayWeekMinMap[day.dayOfWeek]}
          key={day.day}
          isSelected={day.day === daySelected}
          onClick={() => onClick(day)}
        />
      ))}
    </div>
  );
};

// Helper function to get 5 days centered around the selected day
function getReducedDays(days: any[], selectedDay: number): any[] {
  // Find the index of the selected day
  const selectedIndex = days.findIndex((day) => day.day === selectedDay);

  // If selected day not found, return first 5 days
  if (selectedIndex === -1) return days.slice(0, 5);

  // Calculate start and end indices to get 5 days centered around selected day
  let startIndex = Math.max(0, selectedIndex - 2);
  let endIndex = startIndex + 5;

  // Adjust if we go beyond the array bounds
  if (endIndex > days.length) {
    endIndex = days.length;
    startIndex = Math.max(0, endIndex - 5);
  }

  return days.slice(startIndex, endIndex);
}
