import { Day } from "@jocom/types";
import React from "react";

interface props {
  day: Day;
  dayWeekMin: string;
  isSelected: boolean;
  onClick: (day: Day) => void;
}

export const WeekResumeItem = ({
  day,
  dayWeekMin,
  isSelected,
  onClick,
}: props) => {
  return (
    <>
      {!isSelected && (
        <button
          key={day.day}
          className="flex group hover:bg-blue-100 hover:shadow-lg hover-light-shadow rounded-lg mx-1 transition-all	duration-300	 cursor-pointer justify-center w-16"
          onClick={() => {
            onClick(day);
          }}
        >
          <div className="flex items-center px-4 py-4">
            <div className="text-center">
              <p className=" group-hover:text-blue-900 text-sm transition-all	duration-300">
                {" "}
                {dayWeekMin}{" "}
              </p>
              <p className=" group-hover:text-blue-900 mt-3 group-hover:font-bold transition-all	duration-300">
                {" "}
                {day.day}{" "}
              </p>
            </div>
          </div>
        </button>
      )}
      {isSelected && (
        <button
          key={day.day}
          className="flex group bg-blue-300 shadow-lg light-shadow rounded-lg mx-1 cursor-pointer justify-center relative w-16 content-center"
          onClick={() => {
            onClick(day);
          }}
        >
          <span className="flex h-3 w-3 absolute -top-1 -right-1">
            <span className="animate-ping absolute group-hover:opacity-75 opacity-0 inline-flex h-full w-full rounded-full bg-blue-400 "></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-blue-500"></span>
          </span>
          <div className="flex items-center px-4 py-4">
            <div className="text-center">
              <p className="text-blue-900 text-sm"> {dayWeekMin} </p>
              <p className="text-blue-900  mt-3 font-bold"> {day.day} </p>
            </div>
          </div>
        </button>
      )}
    </>
  );
};
