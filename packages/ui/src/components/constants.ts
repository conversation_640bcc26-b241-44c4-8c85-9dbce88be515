
export enum FormNumberColumnsEnum {
    ONE = "mb-4",
    TWO = "grid md:grid-cols-2 xs:grid-cols-1 gap-x-8 gap-y-6 ",
    THREE="grid md:grid-cols-3 xs:grid-cols-1 gap-x-4 gap-y-6 ",
}

export enum InputTypeEnum{
    TEXT = "text", 
    EMAIL = "email", 
    PASSWORD = "password",
    DATETIME_LOCAL = "DATETIME_LOCAL"
}

export enum InputTakeColumns{
    ONE = "",
    TWO = " "
}

export enum NumberOfGridColumns{
    ONE="grid grid-cols-1",
    TWO="grid grid-cols-2",
    THREE="grid grid-cols-3",
    FOUR="grid grid-cols-4",
    FIVE="grid grid-cols-5",
    SIX="grid grid-cols-6",
}

export enum ButtonType{
    BUTTON = "button", 
    SUBMIT= "submit"
}


export enum ChuletazoStatusCode {
    SUCCESS=0,
    UNAUTHORIZED = 1,
    NOTFOUND = 2,
    INACTIVE = 3, 
    EXPIRED = 4, 
    REJECTED = 5, 
    UNSUCCESS = 99
}

export enum UserRole{
    ADMIN = "admin",
    DOCTOR = "doctor",
    NURSE = "nurse",
    RECEPTIONIST = "receptionist",
    ACCOUNTANT = "accountant",
    CLIENT = "client",
}


export const UserRoleOptions: Array<{ id: string; text: string }> = [
    { id: UserRole.ADMIN, text: "Administrador" },
    { id: UserRole.DOCTOR, text: "Doctor" },
    { id: UserRole.NURSE, text: "Enfermera" },
    { id: UserRole.RECEPTIONIST, text: "Recepcionista" },
    { id: UserRole.ACCOUNTANT, text: "Contador" },
    { id: UserRole.CLIENT, text: "Cliente" },
  ];

export enum EventStatus {
  PENDING = "PENDIENTE",
  INVITED = "INVITADO",
  ACCEPTED = "ACEPTADO",
  DECLINED = "NO ACEPTADO",
  MAYBE = "TALVEZ",
}


export enum ReminderStatus {
  PENDING = 'pending',
  SENT = 'sent',
  VIEWED = 'viewed',
  ERROR = 'error', 
  DISMISSED = 'dismissed'
}

export enum ReminderMethod {
  EMAIL = 'email',
  IN_APP = 'in-app',
  PUSH = 'push'
}


  export enum DayWeek {
  MONDAY = "Lunes",
  TUESDAY = "Martes",
  WEDNESDAY = "Miércoles",
  THURSDAY = "Jueves",
  FRIDAY = "Viernes",
  SATURDAY = "Sábado",
  SUNDAY = "DOMINGO",
}

export enum DayWeekMin {
  MONDAY = "Lun",
  TUESDAY = "Mar",
  WEDNESDAY = "Miérc",
  THURSDAY = "Juev",
  FRIDAY = "Vier",
  SATURDAY = "Sáb",
  SUNDAY = "Do",
}

export const DayWeekMap: Record<number, DayWeek> = {
    0: DayWeek.SUNDAY,    // Sunday
    1: DayWeek.MONDAY,    // Monday
    2: DayWeek.TUESDAY,   // Tuesday
    3: DayWeek.WEDNESDAY, // Wednesday
    4: DayWeek.THURSDAY,  // Thursday
    5: DayWeek.FRIDAY,    // Friday
    6: DayWeek.SATURDAY   // Saturday
  };

export const DayWeekMinMap: Record<number, DayWeekMin> = {
    0: DayWeekMin.SUNDAY,    // Sunday
    1: DayWeekMin.MONDAY,    // Monday
    2: DayWeekMin.TUESDAY,   // Tuesday
    3: DayWeekMin.WEDNESDAY, // Wednesday
    4: DayWeekMin.THURSDAY,  // Thursday
    5: DayWeekMin.FRIDAY,    // Friday
    6: DayWeekMin.SATURDAY   // Saturday
  };

  export const MonthsMap: Record<number, string> = {
    1: "Enero",
    2: "Febrero",
    3: "Marzo",
    4: "Abril",
    5: "Mayo",
    6: "Junio",
    7: "Julio",
    8: "Agosto",
    9: "Septiembre",
    10: "Octubre",
    11: "Noviembre",
    12: "Diciembre",
  };

export enum ColorEvent {
  BLUE = "bg-blue-400 text-white",
  PURPLE = "bg-purple-400 text-white",
}