import React, { useState, useEffect } from "react";
import { InputTakeColumns } from "../constants";

interface DatePickerProps {
  id: string;
  label: string;
  initialDate: Date;
  onChange?: (date: Date) => void;
  inputTakeColumns?: InputTakeColumns;
  required?: boolean;
}

export const DatePicker = ({
  id,
  label,
  initialDate,
  onChange,
  inputTakeColumns = InputTakeColumns.ONE,
  required = false,
}: DatePickerProps) => {
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);

  // Update internal state when initialDate prop changes
  useEffect(() => {
    setSelectedDate(new Date(initialDate));
  }, [initialDate]);

  // Format date for date input (YYYY-MM-DD)
  const formatDateForInput = (date: Date): string => {
    console.log("\n\n\n\n\nformatDateForInput", date);

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    console.log(`${year}-${month}-${day}`);

    return `${year}-${month}-${day}`;
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const [year, month, day] = e.target.value.split("-").map(Number);

    // Create a new date with the selected date but keep the current time
    const newDate = new Date(selectedDate);
    newDate.setFullYear(year);
    newDate.setMonth(month - 1);
    newDate.setDate(day);

    setSelectedDate(newDate);
    onChange?.(newDate);
  };

  return (
    <div className={inputTakeColumns}>
      <label htmlFor={id} className="block text-sm font-semibold leading-6">
        {label}
      </label>
      <div className="mt-2.5">
        <input
          type="date"
          id={id}
          name={id}
          required={required}
          value={formatDateForInput(selectedDate)}
          onChange={handleDateChange}
          className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  );
};
