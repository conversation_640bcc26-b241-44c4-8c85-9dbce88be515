import React, { useState, useEffect } from "react";
import { InputTakeColumns } from "../constants";

interface DateTimePickerProps {
  id: string;
  label: string;
  initialDate: Date;
  onChange?: (date: Date) => void;
  inputTakeColumns?: InputTakeColumns;
  required?: boolean;
}

export const DateTimePicker = ({
  id,
  label,
  initialDate,
  onChange,
  inputTakeColumns = InputTakeColumns.ONE,
  required = false,
}: DateTimePickerProps) => {
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);

  // Update internal state when initialDate prop changes
  useEffect(() => {
    setSelectedDate(new Date(initialDate));
  }, [initialDate]);

  // Format date for date input (YYYY-MM-DD)
  const formatDateForInput = (date: Date): string => {
    // Create a new date to avoid timezone issues
    const d = new Date(date);
    const year = d.getFullYear();
    // Month is 0-indexed in JS, so add 1 and pad with 0 if needed
    const month = String(d.getMonth() + 1).padStart(2, "0");
    // Pad day with 0 if needed
    const day = String(d.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Format time for time input (HH:MM)
  const formatTimeForInput = (date: Date): string => {
    return `${String(date.getHours()).padStart(2, "0")}:${String(
      date.getMinutes()
    ).padStart(2, "0")}`;
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Create a new date object based on the current selected date
    const newDate = new Date(selectedDate);

    // Parse the date parts from the input value
    const [year, month, day] = e.target.value.split("-").map(Number);

    // Set the date parts (month is 0-indexed in JS Date)
    newDate.setFullYear(year);
    newDate.setMonth(month - 1);
    newDate.setDate(day);

    // Create a fresh date object to avoid any reference issues
    const updatedDate = new Date(newDate);

    setSelectedDate(updatedDate);
    onChange?.(updatedDate);
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Create a new date object based on the current selected date
    const newDate = new Date(selectedDate);

    // Parse the time parts from the input value
    const [hours, minutes] = e.target.value.split(":").map(Number);

    // Set the time parts
    newDate.setHours(hours);
    newDate.setMinutes(minutes);

    // Create a fresh date object to avoid any reference issues
    const updatedDate = new Date(newDate);

    setSelectedDate(updatedDate);
    onChange?.(updatedDate);
  };

  return (
    <div className={inputTakeColumns}>
      <label
        htmlFor={`${id}-date`}
        className="block text-sm font-semibold leading-6"
      >
        {label}
      </label>
      <div className="mt-2.5 grid grid-cols-2 gap-4">
        <div>
          <label
            htmlFor={`${id}-date`}
            className="block text-xs text-gray-500 dark:text-gray-400 mb-1"
          >
            Fecha
          </label>
          <input
            type="date"
            id={`${id}-date`}
            name={`${id}-date`}
            required={required}
            value={formatDateForInput(selectedDate)}
            onChange={handleDateChange}
            className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div>
          <label
            htmlFor={`${id}-time`}
            className="block text-xs text-gray-500 dark:text-gray-400 mb-1"
          >
            Hora
          </label>
          <input
            type="time"
            id={`${id}-time`}
            name={`${id}-time`}
            required={required}
            value={formatTimeForInput(selectedDate)}
            onChange={handleTimeChange}
            className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>
    </div>
  );
};
