import React from "react";

interface Props {
  children: React.ReactNode;
  title: string;
  description: string;
  submitText: string;
  actionForm: (formData: FormData) => void;
}

export const Form = ({
  children,
  title,
  description,
  actionForm,
  submitText,
}: Props) => {
  function handleSubmitForm(event: React.SyntheticEvent<HTMLFormElement>) {
    event.preventDefault();
    const form = new FormData(event.currentTarget);
    actionForm(form);
  }

  return (
    <div className="isolate  px-6 py-4 sm:py-6 lg:px-6">
      <div className="mx-auto max-w-2xl text-center">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
          {title}
        </h2>
        <p className="mt-2 text-lg leading-8 ">{description}</p>
      </div>
      <form
        onSubmit={handleSubmitForm}
        className="mx-auto mt-16 max-w-(--breakpoint-2xl) sm:mt-4"
      >
        {children}
        <div className="mt-10">
          <button
            type="submit"
            className="block w-full rounded-md bg-cyan-600 px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-xs hover:bg-green-500 focus-visible:outline-solid focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            {submitText}
          </button>
        </div>
      </form>
    </div>
  );
};
