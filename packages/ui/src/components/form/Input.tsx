import React from "react";
import { InputTakeColumns, InputTypeEnum } from "../constants";

interface Props {
  id: string;
  text: string;
  placeholder: string;
  inputType: InputTypeEnum;
  inputTakeColumns?: InputTakeColumns;
  value?: string;
  required?: boolean;
}

export const Input = ({
  id,
  text,
  placeholder,
  inputType,
  inputTakeColumns = InputTakeColumns.ONE,
  value = "",
  required = false,
}: Props) => {
  return (
    <div className={inputTakeColumns}>
      <label htmlFor={id} className="block text-sm font-semibold leading-6">
        {text}
      </label>
      <div className="mt-2.5">
        <input
          type={inputType}
          required={required}
          name={id}
          id={id}
          defaultValue={value}
          placeholder={placeholder}
          className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs  ring-1 ring-inset  focus:ring-2 focus:ring-inset  sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  );
};
