import React from "react";
import { InputTakeColumns } from "../constants";

interface Props {
  id: string;
  text: string;
  placeholder: string;
  inputTakeColumns?: InputTakeColumns;
  value: string;
}

export const InputTextArea = ({
  id,
  text,
  placeholder,
  inputTakeColumns = InputTakeColumns.ONE,
  value,
}: Props) => {
  return (
    <div className={inputTakeColumns}>
      <label htmlFor={id} className="block text-sm font-semibold leading-6">
        {text}
      </label>
      <div className="mt-2.5">
        <textarea
          name={id}
          id={id}
          defaultValue={value}
          placeholder={placeholder}
          className="block w-full rounded-md border-0 px-4 py-4 shadow-xs  ring-1 ring-inset  focus:ring-2 focus:ring-inset  sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  );
};
