import React from "react";
import { InputTakeColumns } from "../constants";

interface Props {
  id: string;
  text: string;
  placeholder: string;
  inputTakeColumns?: InputTakeColumns;
  value?: string;
  options: Array<{ id: string; text: string }>;
}

export const Select = ({
  id,
  text,
  placeholder,
  inputTakeColumns = InputTakeColumns.ONE,
  value = "none",
  options,
}: Props) => {
  return (
    <div className={inputTakeColumns}>
      <label htmlFor={id}>{text}</label>
      <div className="mt-2.5">
        <select
          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          name={id}
          id={id}
          defaultValue={value}
        >
          <option value="none" disabled hidden>
            {placeholder}
          </option>
          {options.map((item) => (
            <option key={item.id} value={item.id}>
              {item.text}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};
