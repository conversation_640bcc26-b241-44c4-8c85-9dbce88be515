import React, { useState, useEffect } from "react";
import { InputTakeColumns } from "../constants";

interface TimePickerProps {
  id: string;
  label: string;
  initialTime: Date;
  onChange?: (time: Date) => void;
  inputTakeColumns?: InputTakeColumns;
  required?: boolean;
}

export const TimePicker = ({
  id,
  label,
  initialTime,
  onChange,
  inputTakeColumns = InputTakeColumns.ONE,
  required = false,
}: TimePickerProps) => {
  const [selectedTime, setSelectedTime] = useState<Date>(initialTime);

  // Update internal state when initialTime prop changes
  useEffect(() => {
    setSelectedTime(new Date(initialTime));
  }, [initialTime]);

  // Format time for time input (HH:MM)
  const formatTimeForInput = (date: Date): string => {
    return `${String(date.getHours()).padStart(2, "0")}:${String(
      date.getMinutes()
    ).padStart(2, "0")}`;
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const [hours, minutes] = e.target.value.split(":").map(Number);
    
    // Create a new date with the current date but update the time
    const newTime = new Date(selectedTime);
    newTime.setHours(hours);
    newTime.setMinutes(minutes);
    
    setSelectedTime(newTime);
    onChange?.(newTime);
  };

  return (
    <div className={inputTakeColumns}>
      <label
        htmlFor={id}
        className="block text-sm font-semibold leading-6"
      >
        {label}
      </label>
      <div className="mt-2.5">
        <input
          type="time"
          id={id}
          name={id}
          required={required}
          value={formatTimeForInput(selectedTime)}
          onChange={handleTimeChange}
          className="block w-full rounded-md border-0 px-3.5 py-2 shadow-xs ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  );
};