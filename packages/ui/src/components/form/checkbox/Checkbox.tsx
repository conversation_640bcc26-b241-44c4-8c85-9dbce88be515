import React from "react";

interface Props {
  options: Array<string>;
  text: string;
}

export const Checkbox = ({ options, text }: Props) => {
  return (
    <div className="flex flex-col items-center justify-center ">
      <p>{text}</p>

      <div className="flex flex-col">
        {options.map((item) => (
          <label key={item} className="inline-flex items-center mt-3">
            <input
              type="checkbox"
              className="form-checkbox h-5 w-5 text-gray-600"
              defaultChecked
            />
            <span className="ml-2 text-gray-700">{item}</span>
          </label>
        ))}
      </div>
    </div>
  );
};
