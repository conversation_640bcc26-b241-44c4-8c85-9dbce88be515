import React from "react";

export interface InputRadioProps {
  inputText: string;
  text1: string;
  text2: string;
}

export const InputRadio = ({ inputText, text1, text2 }: InputRadioProps) => {
  return (
    <div>
      <label className="mb-3 block text-base font-medium text-[#07074D]">
        {inputText}
      </label>
      <div className="flex items-center space-x-6">
        <div className="flex items-center">
          <input
            type="radio"
            name="radio1"
            id="radioButton1"
            className="h-5 w-5"
          />
          <label
            htmlFor="radioButton1"
            className="pl-3 text-base font-medium text-[#07074D]"
          >
            {text1}
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            name="radio1"
            id="radioButton2"
            className="h-5 w-5"
          />
          <label
            htmlFor="radioButton2"
            className="pl-3 text-base font-medium text-[#07074D]"
          >
            {text2}
          </label>
        </div>
      </div>
    </div>
  );
};
