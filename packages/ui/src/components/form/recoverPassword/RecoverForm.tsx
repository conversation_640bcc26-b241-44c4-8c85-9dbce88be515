"use client";

import React from "react";
import { Form } from "../Form";
import { FormDiv } from "../FormDiv";
import { Input } from "../Input";
import { FormNumberColumnsEnum, InputTypeEnum } from "../../constants";

interface props {
  token: string;
}

export function RecoverPassword({ token }: props) {
  return (
    <div className="container max-w-full mx-auto md:py-24 px-12">
      <div className="max-w-sm mx-auto ">
        <Form
          title={"Establece tu password"}
          description={
            "Ingresa tu nueva contraseña. Después podrás entrar a esta con tu correo electrónico." +
            token
          }
          submitText={"Establecer password"}
          actionForm={function (formData: FormData): void {
            console.log(formData);
          }}
        >
          <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
            <Input
              id={"password1"}
              text={"Contraseña:"}
              placeholder={""}
              inputType={InputTypeEnum.PASSWORD}
            />
          </FormDiv>
          <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
            <Input
              id={"password2"}
              text={"Confirma contraseña"}
              placeholder={""}
              inputType={InputTypeEnum.PASSWORD}
            />
          </FormDiv>
        </Form>
      </div>
    </div>
  );
}
