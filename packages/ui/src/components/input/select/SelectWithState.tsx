"use client";
import React from "react";
import { CgArrowsV } from "react-icons/cg";
type Props = {
  data: Array<{ key: string; value: string }>;
  value: string;
  changeHandler(value: string): void;
  text: string;
  id: string;
};

export const SelectWithState = ({
  text,
  id,
  data,
  value,
  changeHandler,
}: Props) => {
  const onChangeHandler = (event: React.ChangeEvent<HTMLSelectElement>) => {
    event.preventDefault();
    const newValue = event.target.value;
    changeHandler(newValue);
  };

  return (
    <div className="w-full max-w-sm ">
      <div className="grid md:grid-cols-2">
        <label className="hidden md:block text-right my-auto mx-4" htmlFor={id}>
          {text}
        </label>
        <div className="relative">
          <select
            className="w-full bg-transparent text-sm border rounded pl-3 pr-8 py-2 transition duration-300 ease focus:outline-none shadow-sm focus:shadow-md appearance-none cursor-pointer"
            onChange={onChangeHandler}
            value={value}
            name={id}
            id={id}
          >
            {data.map((item) => (
              <option key={item.key} value={item.key}>
                {item.value}
              </option>
            ))}
          </select>
          <CgArrowsV className="h-5 w-5 absolute top-2.5 right-2.5" />
        </div>
      </div>
    </div>
  );
};
