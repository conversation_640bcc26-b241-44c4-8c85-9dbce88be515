"use client";
import { signOut } from "next-auth/react";
import React from "react";
import { MdLogout } from "react-icons/md";

interface LogoutProps {
  onLogout?: () => void;
}

export const LogoutNavbar = ({ onLogout }: LogoutProps) => {
  const handleLogout = async () => {
    // Call the optional onLogout callback if provided
    if (onLogout) {
      onLogout();
    }

    // Then sign out using next-auth
    await signOut();
  };

  return (
    <button
      onClick={handleLogout}
      className="flex items-center mr-4 hover:text-blue-100"
    >
      <span className="inline-flex mr-1">
        <MdLogout className="w-5 h-5" />
      </span>
      Salir
    </button>
  );
};
