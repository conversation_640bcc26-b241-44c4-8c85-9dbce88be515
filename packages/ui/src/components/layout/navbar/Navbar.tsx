"use client";
import React from "react";
import { ToggleDark } from "../sidebar/toggleDark/ToggleDark";
import { LogoutNavbar } from "./LogoutNavbar";
import { NavbarUser } from "./NavbarUser";
import { StoredUser } from "@jocom/types";
interface props {
  navBarElement: React.ReactNode | null;
  navBarElement2?: React.ReactNode | null;
  onLogout?: () => void;
  user: StoredUser | null;
}

export const Navbar = ({
  navBarElement,
  onLogout,
  user,
  navBarElement2 = null,
}: props) => {
  if (!user) {
    return <></>;
  }
  return (
    <div className="fixed w-full flex items-center justify-between h-14 text-white z-10">
      <div className="flex items-center justify-start md:justify-center pl-3 w-14 md:w-64 h-14 bg-blue-800 dark:bg-gray-800 border-none">
        <NavbarUser
          name={user.name || ""}
          email={user.email}
          path={user.image || "/avatar/avatar-01.svg"}
        />
      </div>
      <div className="flex justify-between items-center h-14 bg-blue-800 dark:bg-gray-800 header-right">
        <div className="max-w-2xl mx-auto">
          {navBarElement && navBarElement}
        </div>
        <div className="max-w-2xl mx-auto">
          {navBarElement2 && navBarElement2}
        </div>

        <ul className="flex items-center">
          <li>
            <ToggleDark />
          </li>
          <li>
            <div className="block w-px h-6 mx-3 bg-gray-400 dark:bg-gray-700"></div>
          </li>
          <li>
            <LogoutNavbar onLogout={onLogout} />
          </li>
        </ul>
      </div>
    </div>
  );
};
