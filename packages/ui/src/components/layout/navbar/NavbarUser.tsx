import Image from "next/image";
import React from "react";

interface Props {
  name: string;
  email: string;
  path: string;
}

export const NavbarUser = ({ name, email, path }: Props) => {
  return (
    <div className="flex items-center justify-start md:justify-start pl-3 w-14 md:w-64 h-14 bg-blue-800 dark:bg-gray-800 border-none">
      <Image
        className="w-7 h-7 md:w-10 md:h-10 mr-2 rounded-md overflow-hidden"
        src={path}
        alt=""
        width={50}
        height={50}
      />
      <div className="hidden  md:block">
        <div className="font-medium text-white">{name}</div>
        <div className="text-white">{email}</div>
      </div>
    </div>
  );
};
