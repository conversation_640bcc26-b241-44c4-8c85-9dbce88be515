"use client";

import { EventReminder, RemindersResponse } from "@jocom/types";
import { useState, useEffect, useRef } from "react";
import { ReminderStatus } from "../../constants";
import { BsClockHistory } from "react-icons/bs";

// Determine color based on time difference
const getColorClass = (timeDiff: number): string => {
  if (timeDiff <= 5 && timeDiff > 0) return "bg-orange-500";
  if (timeDiff <= 10 && timeDiff > 5) return "bg-yellow-500";
  if (timeDiff <= 0 && timeDiff > -5) return "bg-green-500";
  if (timeDiff <= -5) return "bg-red-500";
  return "bg-blue-500"; // Default for more than 10 mins away
};

const responseFree: React.ReactNode = (
  <div
    className={`flex items-center px-3 py-2 rounded-full text-white ${getColorClass(
      0
    )} hover:opacity-90 transition-opacity`}
  >
    LIBRE
  </div>
);

export const ReminderMenu = ({ reminders }: RemindersResponse) => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [activeReminder, setActiveReminder] = useState<EventReminder | null>(
    null
  );
  const reminderRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Handle click outside to close the popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        activeReminder &&
        reminderRef.current &&
        !reminderRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setActiveReminder(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [activeReminder]);

  // Find the next upcoming appointment
  const getNextAppointment = (): EventReminder | null => {
    if (!reminders || reminders.length === 0) return null;

    const now = currentTime.getTime();
    const upcoming = reminders
      .filter(
        (appt) =>
          appt.status !== ReminderStatus.DISMISSED &&
          new Date(appt.remindAt).getTime() > now - 5 * 60000
      ) // Include appointments up to 5 mins past
      .sort(
        (a, b) =>
          new Date(a.remindAt).getTime() - new Date(b.remindAt).getTime()
      );

    return upcoming.length > 0 ? upcoming[0] : null;
  };

  const nextAppointment = getNextAppointment();

  if (!nextAppointment) return responseFree;

  const appointmentTime = new Date(nextAppointment.remindAt);
  const timeDiff = (appointmentTime.getTime() - currentTime.getTime()) / 60000; // Difference in minutes

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const handleReminderClick = (): void => {
    setActiveReminder(activeReminder ? null : nextAppointment);
  };

  const handleMarkAsViewed = (): void => {
    // In a real app, you would update the appointment's viewed status in your state/API
    setActiveReminder(null);
    // Call to update the appointment as viewed would go here
  };

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={handleReminderClick}
        className={`flex items-center px-3 py-2 rounded-full text-white ${getColorClass(
          timeDiff
        )} hover:opacity-90 transition-opacity`}
      >
        <span className="mr-2 font-medium">{formatTime(appointmentTime)}</span>
        <BsClockHistory />
      </button>

      {/* Reminder details popup */}
      {activeReminder && (
        <div
          ref={reminderRef}
          className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 overflow-hidden dark:bg-gray-800"
        >
          <div className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {nextAppointment.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {appointmentTime.toLocaleString([], {
                    weekday: "short",
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${getColorClass(
                  timeDiff
                )} text-white`}
              >
                {timeDiff > 0 ? `In ${Math.round(timeDiff)} mins` : "Now"}
              </span>
            </div>

            {nextAppointment.description && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                {nextAppointment.description}
              </p>
            )}

            <div className="mt-4 flex justify-end">
              <button
                onClick={handleMarkAsViewed}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
              >
                Mark as Viewed
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
