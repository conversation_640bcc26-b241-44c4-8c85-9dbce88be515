"use client";

import { DayInfo } from "@jocom/types";
import { Dispatch, SetStateAction } from "react";
import { WeekResume } from "../../calendar";
import { getWeekInfoBasedOnDay } from "../../calendar-helper";
import { MonthsMap, DayWeekMap } from "../../constants";
import { Loading } from "../../loading";
import { H2 } from "../../text";

interface props {
  children: React.ReactNode;
  setSelectedDay: Dispatch<SetStateAction<DayInfo>>;
  selectedDay: DayInfo;
  isLoading?: boolean;
}

/**
 * add this state to the principal component const [selectedDay, setSelectedDay] = useState<DayInfo>(getCurrentDayInfo()); and pas the constants
 * @param param0
 * @returns
 */
export const SelectDayLayout = ({
  children,
  setSelectedDay,
  selectedDay,
  isLoading = false,
}: props) => {
  const weekInfo = getWeekInfoBasedOnDay(selectedDay);

  const handleDayClick = (day: DayInfo) => {
    setSelectedDay(day);
  };

  return (
    <>
      <div className="h-screen md:p-6">
        <H2
          text={`${MonthsMap[selectedDay.month]} - ${
            DayWeekMap[selectedDay.dayOfWeek]
          }`}
        />
        <div className="">
          <div className="flex justify-center my-4">
            <WeekResume
              weekInfo={weekInfo}
              daySelected={selectedDay.day}
              onClick={handleDayClick}
            />
          </div>
          {isLoading ? (
            <Loading size="large" message="Por favor espere..." />
          ) : (
            children
          )}
        </div>
      </div>
    </>
  );
};
