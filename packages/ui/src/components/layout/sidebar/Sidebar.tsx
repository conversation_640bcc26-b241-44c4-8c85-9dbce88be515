import React from "react";
import { PropsSideBarItem } from "./SidebarItem";
import { SidebarDivision } from "./SidebarDivision";

export interface SideBarMenuDivision {
  subMenuText: string;
  subMenuItems: Array<PropsSideBarItem>;
}

export type SideBarMenu = Array<SideBarMenuDivision>;

interface Props {
  sideBarMenu: SideBarMenu;
}

export const Sidebar = ({ sideBarMenu }: Props) => {
  return (
    <div className="fixed flex flex-col top-14 left-0 w-14 hover:w-64 md:w-64 bg-blue-900 dark:bg-gray-900 h-full text-white transition-all duration-300 border-none z-10 sidebar">
      <div className="overflow-y-auto overflow-x-hidden flex flex-col justify-between grow">
        <ul className="flex flex-col py-4 space-y-1">
          {sideBarMenu.map((subMenu) => (
            <SidebarDivision
              key={subMenu.subMenuText}
              subMenuText={subMenu.subMenuText}
              subMenuItems={subMenu.subMenuItems}
            />
          ))}
        </ul>
        <p className="mb-14 px-5 py-3 hidden md:block text-center text-xs">
          Copyright @2025
        </p>
      </div>
    </div>
  );
};
