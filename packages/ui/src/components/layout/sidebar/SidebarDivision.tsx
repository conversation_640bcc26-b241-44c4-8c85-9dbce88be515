import React from "react";
import { SideBarMenuDivision } from "./Sidebar";
import { SidebarTextDivision } from "./SidebarTextDivision";
import { SidebarItem } from "./SidebarItem";

export const SidebarDivision = ({
  subMenuText,
  subMenuItems,
}: SideBarMenuDivision) => {
  return (
    <>
      <SidebarTextDivision text={subMenuText} />
      {subMenuItems.map((item) => (
        <SidebarItem
          path={item.path}
          icon={item.icon}
          text={item.text}
          key={item.text}
          notification={null}
        />
      ))}
    </>
  );
};
