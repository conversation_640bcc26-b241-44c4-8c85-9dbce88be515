import Link from "next/link";
import React, { JSX } from "react";

export interface PropsSideBarItem {
  path: string;
  text: string;
  notification: string | null;
  icon: JSX.Element;
}

export const SidebarItem = ({
  path,
  text,
  notification,
  icon,
}: PropsSideBarItem) => {
  return (
    <li>
      <Link
        href={path}
        className="relative flex flex-row items-center h-11 focus:outline-hidden hover:bg-blue-800 dark:hover:bg-gray-600 text-white-600 hover:text-white-800 border-l-4 border-transparent hover:border-blue-500 dark:hover:border-gray-800 pr-6"
      >
        <div className="inline-flex justify-center items-center ml-4">
          <div className="w-5 h-5">{icon}</div>
        </div>
        <span className="ml-2 text-sm tracking-wide truncate">{text}</span>
        {notification && (
          <span className="hidden md:block px-2 py-0.5 ml-auto text-xs font-medium tracking-wide text-blue-500 bg-indigo-50 rounded-full">
            {notification}
          </span>
        )}
      </Link>
    </li>
  );
};
