"use client";

import React, { useState, useEffect } from "react";
import { IsDark } from "./IsDark";
import { IsNotDark } from "./IsNotDark";

const getThemeIsDark = (): boolean => {
  if (typeof window === "undefined") return false;
  const itemGot = window.localStorage.getItem("dark");
  if (itemGot !== null) {
    return JSON.parse(itemGot);
  }
  return (
    !!window.matchMedia &&
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );
};

export const ToggleDark = () => {
  const [isDark, setIsDark] = useState(false);

  // Sync state and <html> class on mount
  useEffect(() => {
    const dark = getThemeIsDark();
    setIsDark(dark);
    document.documentElement.classList.toggle("dark", dark);
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    const media = window.matchMedia("(prefers-color-scheme: dark)");
    const handler = (e: MediaQueryListEvent) => {
      if (window.localStorage.getItem("dark") === null) {
        setIsDark(e.matches);
        document.documentElement.classList.toggle("dark", e.matches);
      }
    };
    media.addEventListener("change", handler);
    return () => media.removeEventListener("change", handler);
  }, []);

  // Update localStorage and <html> class when isDark changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem("dark", JSON.stringify(isDark));
      document.documentElement.classList.toggle("dark", isDark);
    }
  }, [isDark]);

  const handleOnClick = () => {
    setIsDark((prev) => !prev);
  };

  return (
    <button
      type="button"
      aria-label={isDark ? "Switch to light mode" : "Switch to dark mode"}
      className="group p-2 transition-colors duration-200 rounded-full shadow-md bg-blue-200 hover:bg-blue-200 dark:bg-gray-50 dark:hover:bg-gray-200 text-gray-900 focus:outline-none"
      onClick={handleOnClick}
    >
      {isDark ? <IsDark /> : <IsNotDark />}
    </button>
  );
};
