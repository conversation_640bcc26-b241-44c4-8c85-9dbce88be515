import React from "react";
import { SingleElementListItem } from "./SingleElementListItem";
import { EventsResponse } from "@jocom/types";

interface props {
  events: EventsResponse[];
}

function getTimeFromISOString(isoString: string): string {
  const date = new Date(isoString);

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  //const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${hours}:${minutes}`;
}

export const SingleElementList = ({ events }: props) => {
  return (
    <div className="md:py-8 py-5 md:px-16 px-5 bg-gray-50 dark:bg-gray-600  rounded-b md:mx-12">
      {events.length > 0 ? (
        <ol className="list-none px-4">
          {events.map((event1) => (
            <SingleElementListItem
              key={event1.id}
              hourStart={getTimeFromISOString(event1.startTime)}
              hourEnd={getTimeFromISOString(event1.endTime)}
              title={event1.title}
              description={event1.description || ""}
              id={event1.id}
              path={`/calendar/event/${event1.id}`}
              attendees={event1.attendees}
              owner={event1.createdBy}
            />
          ))}
        </ol>
      ) : (
        <div className="text-center py-4">No events found for this day</div>
      )}
    </div>
  );
};
