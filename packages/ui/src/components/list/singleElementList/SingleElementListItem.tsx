import { AttendeeResponse } from "@jocom/types";
import Link from "next/link";
import { AiOutlineSchedule } from "react-icons/ai";
import SingleElementListItemAttendees from "./SingleElementListItemAttendees";

interface props {
  hourStart: string;
  hourEnd: string;
  title: string;
  description: string;
  id: string;
  path: string;
  attendees: AttendeeResponse[];
  owner: string;
}

export const SingleElementListItem = ({
  hourStart,
  hourEnd,
  title,
  description,
  id,
  path,
  attendees,
  owner,
}: props) => {
  return (
    <li
      key={id}
      className="border-b pb-4 border-gray-600 dark:border-gray-50 border-dashed pt-5 hover:bg-gray-100 dark:hover:bg-gray-700"
    >
      <Link href={path} key={`event-${id}-`}>
        <div className="flex flex-row justify-between items-start">
          {/* Left column - Event details */}
          <div className="flex flex-col w-2/3 pr-4">
            <p className="text-xs font-light leading-3 text-gray-500 dark:text-gray-300">
              <AiOutlineSchedule className="h-7 w-7 inline-block" /> {hourStart}
              - {hourEnd}
            </p>
            <p
              tabIndex={0}
              className="focus:outline-hidden text-lg font-medium leading-5 text-gray-800 dark:text-gray-100 mt-2"
            >
              {title}
            </p>
            <p className="text-sm pt-2 leading-4 text-gray-600 dark:text-gray-300">
              {description}
            </p>
          </div>

          {/* Right column - Attendees */}
          <ul className="flex flex-col w-1/3 space-y-2">
            {attendees.map((attendee) => (
              <SingleElementListItemAttendees
                key={attendee.userId}
                attendee={attendee}
                owner={owner}
              />
            ))}
          </ul>
        </div>
      </Link>
    </li>
  );
};
