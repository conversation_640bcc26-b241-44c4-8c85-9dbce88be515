import React from "react";
import Image from "next/image";
import { AttendeeResponse } from "@jocom/types";

interface props {
  attendee: AttendeeResponse;
  owner: string;
}

function SingleElementListItemAttendees({ attendee, owner }: props) {
  // Function to determine badge color based on status
  const getBadgeColors = (status: string) => {
    switch (status.toLowerCase()) {
      case "accepted":
        return {
          bg: "bg-green-50",
          text: "text-green-700",
          ring: "ring-green-600/20",
        };
      case "declined":
        return {
          bg: "bg-red-50",
          text: "text-red-700",
          ring: "ring-red-600/20",
        };
      case "maybe":
        return {
          bg: "bg-orange-50",
          text: "text-orange-700",
          ring: "ring-orange-600/20",
        };
      case "invited":
      default:
        return {
          bg: "bg-blue-50",
          text: "text-blue-700",
          ring: "ring-blue-600/20",
        };
    }
  };

  // Get colors based on status
  const statusColors = getBadgeColors(attendee.status);

  // Special case for owner/creator
  const isOwner = owner === attendee.userId;

  return (
    <li className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
      {/* Avatar on the left */}
      <div className="flex-shrink-0">
        <Image
          src={attendee.photoUrl || "/avatars/avatar-01.svg"}
          alt={`Avatar for ${attendee.name}`}
          width={40}
          height={40}
          className="rounded-full object-cover"
        />
      </div>

      {/* User info in the middle */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {attendee.name}
          </p>
          <span
            className={`ml-2 inline-flex items-center rounded-full ${statusColors.bg} px-2 py-0.5 text-xs font-medium ${statusColors.text} ring-1 ring-inset ${statusColors.ring}`}
          >
            {attendee.status}
          </span>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
          {isOwner ? "Creador" : "Invitado"}
        </p>
      </div>
    </li>
  );
}

export default SingleElementListItemAttendees;
