import React from "react";

interface LoadingProps {
  message?: string;
  size?: "small" | "medium" | "large";
}

export const Loading = ({
  message = "Está cargando...",
  size = "medium",
}: LoadingProps) => {
  const sizeClasses = {
    small: "w-6 h-6",
    medium: "w-10 h-10",
    large: "w-16 h-16",
  };

  return (
    <div className="flex flex-col items-center justify-center p-4">
      <div className="relative">
        <div
          className={`${sizeClasses[size]} border-4 border-blue-200 border-t-blue-800 rounded-full animate-spin`}
        ></div>
      </div>
      <p className="mt-4 text-gray-700 dark:text-gray-300 font-medium">
        {message}
      </p>
    </div>
  );
};
