import React, { JSX, useState } from "react";

interface OneLevelDropDownMenuProps {
  buttonLabel: string;
  menuItems: {
    title: string;
    url?: string;
    icon?: JSX.Element;
    action?: () => void;
  }[];
}

const OneLevelDropDownMenu = ({
  buttonLabel,
  menuItems,
}: OneLevelDropDownMenuProps) => {
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen((prev) => !prev);
  };
  return <div>OneLevelDropDownMenu</div>;
};

export default OneLevelDropDownMenu;
