"use client";

import React, { useEffect, useState } from "react";
import { Modal } from "./Modal";
import {
  Form,
  FormDiv,
  Input,
  Select,
  InputTextArea,
  DatePicker,
  TimePicker,
} from "../form";
import {
  EventStatus,
  FormNumberColumnsEnum,
  InputTakeColumns,
  InputTypeEnum,
} from "../constants";
import { Attendee, Day, Events } from "@jocom/types";
import { MdPersonRemoveAlt1 } from "react-icons/md";

interface CreateEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (event: Events, attendees: Attendee[]) => void;
  userId: string;
  clinicId: string;
  selectedDay: Day;
}

function getDateFromDay(day: Day): Date {
  // Get current date to preserve current time
  const currentDate = new Date();

  // Create new date with day's year, month, and day, but current time
  // Note: JavaScript months are 0-indexed (0-11), so we subtract 1 from the month
  return new Date(
    day.year,
    day.month - 1,
    day.day,
    currentDate.getHours(),
    currentDate.getMinutes(),
    currentDate.getSeconds(),
    currentDate.getMilliseconds()
  );
}

function updateTime(date: Date, time: Date): Date {
  // Create a new date with the date parts from 'date' and time parts from 'time'
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    time.getHours(),
    time.getMinutes(),
    0,
    0
  );
}

export const CreateEventModal = ({
  isOpen,
  onClose,
  onSubmit,
  userId,
  clinicId,
  selectedDay,
}: CreateEventModalProps) => {
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [newAttendeeEmail, setNewAttendeeEmail] = useState("");

  // Initialize event date from selected day
  const [eventDate, setEventDate] = useState<Date>(() => {
    return getDateFromDay(selectedDay);
  });

  // Initialize start time
  const [startTime, setStartTime] = useState<Date>(() => {
    return getDateFromDay(selectedDay);
  });

  // Initialize end time to be 30 minutes after start time
  const [endTime, setEndTime] = useState<Date>(() => {
    const time = new Date(getDateFromDay(selectedDay));
    time.setMinutes(time.getMinutes() + 30);
    return time;
  });

  // Update dates when selected day changes
  useEffect(() => {
    // Create date from selected day
    const newDate = getDateFromDay(selectedDay);
    setEventDate(newDate);

    // Reset start time
    setStartTime(new Date(newDate));

    // Reset end time to be 30 minutes after start time
    const newEndTime = new Date(newDate);
    newEndTime.setMinutes(newDate.getMinutes() + 30);
    setEndTime(newEndTime);
  }, [selectedDay]);

  // Handle date change
  const handleDateChange = (date: Date) => {
    setEventDate(date);
  };

  // Handle start time change
  const handleStartTimeChange = (time: Date) => {
    setStartTime(time);

    // If end time is now before start time, update it to be 30 minutes later
    const newStartDateTime = updateTime(eventDate, time);
    const newEndDateTime = updateTime(eventDate, endTime);

    if (newEndDateTime <= newStartDateTime) {
      const adjustedEndTime = new Date(time);
      adjustedEndTime.setMinutes(time.getMinutes() + 30);
      setEndTime(adjustedEndTime);
    }
  };

  // Handle end time change
  const handleEndTimeChange = (time: Date) => {
    // Only accept if it would be after the start time when combined with the event date
    const newStartDateTime = updateTime(eventDate, startTime);
    const newEndDateTime = updateTime(eventDate, time);

    if (newEndDateTime > newStartDateTime) {
      setEndTime(time);
    } else {
      // If it would be before start time, set to 30 minutes after start
      const adjustedEndTime = new Date(startTime);
      adjustedEndTime.setMinutes(startTime.getMinutes() + 30);
      setEndTime(adjustedEndTime);
    }
  };

  if (!isOpen) return null;

  const addAttendee = () => {
    if (newAttendeeEmail.trim() === "") return;

    const newAttendee: Attendee = {
      eventId: "", // Will be set after event creation
      userId: newAttendeeEmail, // Using email as temporary userId
      status: EventStatus.INVITED,
    };

    setAttendees([...attendees, newAttendee]);
    setNewAttendeeEmail("");
  };

  const removeAttendee = (index: number) => {
    const updatedAttendees = [...attendees];
    updatedAttendees.splice(index, 1);
    setAttendees(updatedAttendees);
  };

  const handleSubmit = (formData: FormData) => {
    const title = formData.get("title") as string;
    const description = formData.get("description") as string;
    const location = formData.get("location") as string;
    const calendarId = clinicId;
    const isAllDay = formData.get("isAllDay") === "on";

    // Combine date and times to create start and end datetime
    const startDateTime = updateTime(eventDate, startTime);
    const endDateTime = updateTime(eventDate, endTime);

    console.log("Start datetime:", startDateTime.toISOString());
    console.log("End datetime:", endDateTime.toISOString());

    const newEvent: Events = {
      id: "", // Will be set by backend
      calendarId,
      title,
      description,
      startTime: startDateTime.toISOString(),
      endTime: endDateTime.toISOString(),
      isAllDay,
      location,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: userId,
    };

    // Update eventId for all attendees
    const eventAttendees = attendees.map((attendee) => ({
      ...attendee,
      eventId: newEvent.id,
    }));

    onSubmit(newEvent, eventAttendees);
    onClose();
  };

  return (
    <Modal onClose={onClose}>
      <Form
        title="Crear nuevo evento"
        description="Complete los detalles del evento"
        submitText="Guardar evento"
        actionForm={handleSubmit}
      >
        <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
          <Input
            text="Título"
            placeholder="Título del evento"
            id="title"
            inputType={InputTypeEnum.TEXT}
            required={true}
          />

          <InputTextArea
            text="Descripción"
            placeholder="Descripción del evento"
            id="description"
            value={""}
          />

          {/* Date picker for the event date */}
          <DatePicker
            id="event-date"
            label="Fecha del evento"
            initialDate={eventDate}
            onChange={handleDateChange}
            required={true}
          />

          <div className="flex space-x-4">
            <div className="w-1/2">
              <TimePicker
                id="start-time"
                label="Hora de inicio"
                initialTime={startTime}
                onChange={handleStartTimeChange}
                required={true}
              />
            </div>
            <div className="w-1/2">
              <TimePicker
                id="end-time"
                label="Hora de fin"
                initialTime={endTime}
                onChange={handleEndTimeChange}
                required={true}
              />
            </div>
          </div>

          <div className="flex items-center mt-2">
            <Select
              id={"isAllDay"}
              text={"Todo el día"}
              placeholder={"Es todo el dia"}
              options={[
                { id: "true", text: "Si" },
                { id: "false", text: "No" },
              ]}
            />
          </div>

          <Input
            text="Ubicación"
            placeholder="Ubicación del evento"
            id="location"
            inputType={InputTypeEnum.TEXT}
            required={false}
          />

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Invitar asistentes
            </label>
            <div className="flex mt-1">
              <input
                type="email"
                placeholder="Correo electrónico"
                value={newAttendeeEmail}
                onChange={(e) => setNewAttendeeEmail(e.target.value)}
                className="flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              />
              <button
                type="button"
                onClick={addAttendee}
                className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Agregar
              </button>
            </div>

            {attendees.length > 0 && (
              <div className="mt-3">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Asistentes:
                </h4>
                <ul className="mt-2 space-y-2">
                  {attendees.map((attendee, index) => (
                    <li
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md"
                    >
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {attendee.userId}
                      </span>
                      <button
                        type="button"
                        onClick={() => removeAttendee(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <MdPersonRemoveAlt1 className="h-5 w-5" />
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </FormDiv>
      </Form>
    </Modal>
  );
};
