"use client";

import React from "react";
import { Modal } from "./Modal";
import { Form, FormDiv, Input, Select } from "../form";
import {
  FormNumberColumnsEnum,
  InputTypeEnum,
  UserRoleOptions,
} from "../constants";
import { NewUserClinicData } from "@jocom/types";

interface EmailInputModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (userClinic: NewUserClinicData) => void;
  title?: string;
  description?: string;
  submitText?: string;
  clinicId: string;
}

export const EmailInputModal = ({
  isOpen,
  onClose,
  clinicId,
  onSubmit,
  title = "Nuevo usuario en la clinica",
  description = "Por favor ingrese el correo electrónico",
  submitText = "Continuar",
}: EmailInputModalProps) => {
  if (!isOpen) return null;

  const handleSubmit = (formData: FormData) => {
    const email = formData.get("email") as string;
    const name = formData.get("name") as string;
    const rol = formData.get("rol") as string;
    const userClinic: NewUserClinicData = {
      name,
      email,
      rol,
      clinicId,
    };
    if (email) {
      onSubmit(userClinic);
      onClose();
    }
  };

  return (
    <Modal onClose={onClose}>
      <Form
        title={title}
        description={description}
        submitText={submitText}
        actionForm={handleSubmit}
      >
        <FormDiv numberOfColumns={FormNumberColumnsEnum.ONE}>
          <Input
            text="Nombre y apellido"
            placeholder="Escribe un nombre y un apellido"
            id="name"
            inputType={InputTypeEnum.TEXT}
            required={true}
          />
          <Input
            text="Correo electrónico"
            placeholder="Escribe el correo electrónico"
            id="email"
            inputType={InputTypeEnum.EMAIL}
            required={true}
          />
          <Select
            id={"rol"}
            text={"Rol del usuario"}
            placeholder={"Seleccione el rol del usuario"}
            options={UserRoleOptions}
          />
        </FormDiv>
      </Form>
    </Modal>
  );
};
