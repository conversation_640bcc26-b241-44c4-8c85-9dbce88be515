"use client";
import React, { useEffect, useRef } from "react";

interface Props {
  onClose: () => void;
  children: React.ReactNode;
  maxHeight?: string;
}

export const Modal = ({
  onClose,
  children,
  maxHeight = "max-h-[80vh]",
}: Props) => {
  const modalContentRef = useRef<HTMLDivElement>(null);

  // Handle click outside the modal content
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Only close if clicking directly on the backdrop, not on the modal content
    if (
      modalContentRef.current &&
      !modalContentRef.current.contains(e.target as Node)
    ) {
      onClose();
    }
  };

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    // Add event listener
    document.addEventListener("keydown", handleEscapeKey);

    // Prevent scrolling on the body when modal is open
    document.body.style.overflow = "hidden";

    // Cleanup
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = "auto";
    };
  }, [onClose]);

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity p-4"
      onClick={handleBackdropClick}
      aria-modal="true"
      role="dialog"
    >
      <div
        ref={modalContentRef}
        className={`bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6 max-w-md w-full relative animate-fadeIn ${maxHeight} overflow-y-auto`}
      >
        <button
          className="sticky top-0 float-right text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100 transition-colors"
          onClick={onClose}
          aria-label="Close modal"
        >
          &#x2715;
        </button>
        <div className="clear-both">{children}</div>
      </div>
    </div>
  );
};
