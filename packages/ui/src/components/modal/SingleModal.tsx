"use client";
import React from "react";
import { SingleButton } from "../buttons/SingleButton";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  children: React.ReactNode;
}

export const SingleModal = ({ isOpen, onClose, onOpen, children }: Props) => {
  return (
    <>
      <SingleButton label={"Cambiar Avatar"} handleClick={onOpen} />
      {isOpen && (
        <div className=" fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6 max-w-md w-full relative">
            <button
              className="absolute top-2 right-2 hover:text-gray-400"
              onClick={onClose}
            >
              &#x2715;
            </button>
            {children}
          </div>
        </div>
      )}
    </>
  );
};
