"use client";
import Image from "next/image";
import React from "react";
import { SingleButton } from "../../buttons/SingleButton";

interface Props {
  photoUrl: string;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const SelectAvatar = ({ photoUrl, setIsOpen }: Props) => {
  console.log(photoUrl);

  return (
    <div className="py-3 center mx-auto">
      <div className="bg-gray-200 dark:bg-gray-500 px-4 py-5 rounded-lg shadow-lg text-center w-42">
        <div className="md:col-span-1 h-42 shadow-xl ">
          <div className="flex w-full h-full relative">
            <Image
              src={photoUrl}
              className="w-32 h-32 m-auto"
              alt=""
              width={32}
              height={32}
            />
          </div>
        </div>
        <label className="cursor-pointer pt-6 mt-6">
          <SingleButton
            label={"Cambiar Avatar"}
            handleClick={() => setIsOpen(true)}
          />
        </label>
      </div>
    </div>
  );
};

export default SelectAvatar;
