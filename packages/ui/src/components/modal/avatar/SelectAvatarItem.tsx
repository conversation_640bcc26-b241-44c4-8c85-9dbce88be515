import Image from "next/image";
import React from "react";

interface Props {
  name: string;
  path: string;
  changeAvatar: (path: string) => void;
}

const SelectAvatarItem = ({ name, path, changeAvatar }: Props) => {
  return (
    <li key={`li-${path}`} className="flex flex-col items-center space-y-2">
      <div className="story-ring flex justify-center items-center bg-linear-to-tl from-yellow-200 to-orange-500  rounded-full relative cursor-pointer hover:from-orange-300 hover:to-red-800 transition-all duration-150 delay-100">
        <button
          className="block bg-gray-100 dark:bg-gray-600 p-1 rounded-full"
          onClick={() => changeAvatar(path)}
        >
          <Image
            src={path}
            alt={`Avatar image name: ${name}`}
            width={16}
            height={16}
            className="w-16 rounded-full"
          />
        </button>
      </div>

      <span className="story-text">{name}</span>
    </li>
  );
};

export default SelectAvatarItem;

/*
 * 
MDI (npm i @mdi/font) reuqired for plus icon
<!-- Add Story -->
                    <li className="flex flex-col items-center space-y-2">
                        <!-- Ring -->
                        <div className="story-ring flex justify-center items-center
                                bg-linear-to-tl from-yellow-200 to-orange-500 
                                rounded-full relative cursor-pointer
                                hover:from-orange-300 hover:to-red-400
                                transition-all duration-150 delay-100">
                            <a className="block bg-white p-1 rounded-full" href="#">
                                <!-- Thumbnail -->
                                <img className="w-16 rounded-full"
                                    src="https://images.unsplash.com/photo-1638687110777-b3fcdf2f9c57?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=200&q=80">
                            </a>

                            <button className="absolute transition duration-500 bg-white border-gray-400 
                                    h-8 w-8 rounded-full text-white border-2 
                                    border-white flex justify-center items-center opacity-80
                                    hover:opacity-60">
                            </button>

                            <i className="absolute mdi mdi-plus mdi-18px mx-1 text-gray-500"></i>
                        </div>

                        <!-- Username -->
                        <span className="story-text font-medium">
                            You
                        </span>
                    </li>

 * 
 */
