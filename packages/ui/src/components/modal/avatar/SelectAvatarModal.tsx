"use client";
import React from "react";
import SelectAvatarItem from "./SelectAvatarItem";
import { Modal } from "../Modal";
import { NumberOfGridColumns } from "../../constants";
import { H3 } from "../../text/H3";

const avatars: Array<{ name: string; path: string }> = [
  { name: "Avatar 01", path: "/avatar/avatar-01.svg" },
  { name: "Avatar 02", path: "/avatar/avatar-02.svg" },
  { name: "Avatar 03", path: "/avatar/avatar-03.svg" },
  { name: "Avatar 04", path: "/avatar/avatar-04.svg" },
  { name: "Avatar 05", path: "/avatar/avatar-05.svg" },
  { name: "Avatar 06", path: "/avatar/avatar-06.svg" },
  { name: "Avatar 07", path: "/avatar/avatar-07.svg" },
  { name: "Avatar 08", path: "/avatar/avatar-08.svg" },
  { name: "Avatar 09", path: "/avatar/avatar-09.svg" },
  { name: "Avatar 10", path: "/avatar/avatar-10.svg" },
  { name: "Avatar 11", path: "/avatar/avatar-11.svg" },
  { name: "Avatar 12", path: "/avatar/avatar-12.svg" },
  { name: "Avatar 13", path: "/avatar/avatar-13.svg" },
  { name: "Avatar 14", path: "/avatar/avatar-14.svg" },
  { name: "Avatar 15", path: "/avatar/avatar-15.svg" },
  { name: "Avatar 16", path: "/avatar/avatar-16.svg" },
  { name: "Avatar 17", path: "/avatar/avatar-17.svg" },
  { name: "Avatar 18", path: "/avatar/avatar-18.svg" },
];

interface Props {
  changeAvatar: (path: string) => void;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const SelectAvatarModal = ({
  changeAvatar,

  setIsOpen,
}: Props) => {
  return (
    <Modal onClose={() => setIsOpen(false)}>
      <H3 text={"Seleccione el Avatar de su preferencia"} />

      <div className="w-full max-w-md px-7 py-10 mx-auto bg-gray-100 dark:bg-gray-600 rounded-2xl shadow-xl">
        <ul className={` ${NumberOfGridColumns.FIVE} gap-4 justify-between`}>
          {avatars.map((avatar) => (
            <SelectAvatarItem
              key={avatar.path}
              name={avatar.name}
              path={avatar.path}
              changeAvatar={changeAvatar}
            />
          ))}
        </ul>
      </div>
    </Modal>
  );
};

export default SelectAvatarModal;
