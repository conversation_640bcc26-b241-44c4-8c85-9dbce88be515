import React from "react";
import { NumberOfGridColumns } from "../../constants";

interface InfoInBlocksProps {
  numberOfGridColumns: NumberOfGridColumns;
  information: Array<ItemInfoInBlocks>;
  className?: string;
}

export interface ItemInfoInBlocks {
  title: string;
  text: string;
  icon?: React.ReactNode;
}

export const InfoInBlocks = ({
  numberOfGridColumns,
  information,
  className = "",
}: InfoInBlocksProps) => {
  return (
    <div
      className={`${numberOfGridColumns} gap-4 px-2 w-full mb-6 ${className}`}
    >
      {information.map((item) => (
        <div
          key={item.title}
          className="flex flex-col justify-center rounded-2xl bg-gray-100 dark:bg-gray-600 bg-clip-border px-4 py-5 shadow-md shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors duration-200"
        >
          <div className="flex items-center mb-1">
            {item.icon && <div className="mr-2">{item.icon}</div>}
            <p className="text-sm font-medium text-gray-500 dark:text-gray-300">
              {item.title}
            </p>
          </div>
          <p className="text-lg font-semibold text-navy-700 dark:text-white">
            {item.text}
          </p>
        </div>
      ))}
    </div>
  );
};
