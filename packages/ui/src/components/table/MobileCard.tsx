import React from "react";
import Image from "next/image";
import { ColorState } from "./TableInterface";

interface MobileCardProps {
  title: string;
  value: React.ReactNode;
}

export const MobileCardItem = ({ title, value }: MobileCardProps) => {
  return (
    <div className="flex flex-col mb-2">
      <span className="text-xs font-medium text-gray-500">{title}</span>
      <div className="mt-1">{value}</div>
    </div>
  );
};

interface MobileCardActionsProps {
  children: React.ReactNode;
}

export const MobileCardActions = ({ children }: MobileCardActionsProps) => {
  return (
    <div className="flex justify-end gap-2 mt-3 pt-3 border-t border-gray-100">
      {children}
    </div>
  );
};

interface MobileCardNameImageProps {
  name: string;
  email?: string;
  urlImage: string;
  size?: "sm" | "md" | "lg";
}

export const MobileCardNameImage = ({
  name,
  email,
  urlImage,
  size = "md",
}: MobileCardNameImageProps) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-12 h-12",
  };

  return (
    <div className="flex items-center mb-3">
      <div className={`relative ${sizeClasses[size]} flex-shrink-0 mr-3`}>
        <Image
          className="h-full w-full rounded-full object-cover object-center"
          src={urlImage}
          alt={name}
          width={size === "lg" ? 48 : size === "md" ? 40 : 32}
          height={size === "lg" ? 48 : size === "md" ? 40 : 32}
        />
        <span className="absolute right-0 bottom-0 h-2 w-2 rounded-full bg-green-400 ring-1 ring-white"></span>
      </div>
      <div>
        <div className="font-medium">{name}</div>
        {email && <div className="text-sm text-gray-500">{email}</div>}
      </div>
    </div>
  );
};

interface MobileCardManyOptionsProps {
  options: Array<{
    name: string;
    colorState: ColorState;
  }>;
  maxDisplay?: number;
}

export const MobileCardManyOptions = ({
  options,
  maxDisplay = 5,
}: MobileCardManyOptionsProps) => {
  const displayOptions =
    options.length > maxDisplay ? options.slice(0, maxDisplay) : options;

  return (
    <div className="flex flex-wrap gap-1">
      {displayOptions.map((option) => (
        <span
          key={option.name}
          className={`inline-flex items-center rounded-full bg-${option.colorState}-50 px-2 py-1 text-xs font-semibold text-${option.colorState}-600`}
        >
          {option.name}
        </span>
      ))}
      {options.length > maxDisplay && (
        <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold text-gray-600">
          +{options.length - maxDisplay}
        </span>
      )}
    </div>
  );
};

interface MobileCardStateProps {
  state: string;
  colorState: ColorState;
}

export const MobileCardState = ({
  state,
  colorState,
}: MobileCardStateProps) => {
  return (
    <span
      className={`inline-flex items-center gap-1 rounded-full bg-${colorState}-50 px-2.5 py-0.5 text-xs font-medium text-${colorState}-600`}
    >
      <span className={`h-1.5 w-1.5 rounded-full bg-${colorState}-600`}></span>
      {state}
    </span>
  );
};
