# Table Component

A responsive table component that provides both desktop and mobile views for displaying tabular data.

## Features

- Fully responsive design
- Customizable breakpoint for mobile view
- Separate data structures for desktop and mobile views
- Alternating row colors for better readability
- Dark mode support
- Horizontal scrolling for wide tables on desktop
- Card-based layout for mobile views

## Installation

The Table component is part of the JOCOM UI package. No additional installation is required if you're already using the UI package.

## Basic Usage

```tsx
import { 
  Table, 
  TableBodyRow, 
  TableItemText, 
  TableItemActions 
} from "@jocom/ui/src/components/table";

const MyComponent = () => {
  // Define table headers
  const headerNames = ["Name", "Email", "Actions"];
  
  // Define table rows for desktop view
  const rows: Array<TableBodyRow> = [
    {
      id: "row-1",
      key: "row-1",
      items: [
        <TableItemText id="row-1-name" key="row-1-name" text="John Doe" />,
        <TableItemText id="row-1-email" key="row-1-email" text="<EMAIL>" />,
        <TableItemActions id="row-1-actions" key="row-1-actions" editUrl="/users/1" />
      ]
    }
  ];
  
  return (
    <Table 
      headerNames={headerNames} 
      rows={rows} 
      id="users-table" 
    />
  );
};
```

## Advanced Usage with Mobile View

```tsx
import { 
  Table, 
  TableBodyRow, 
  TableItemText, 
  TableItemActions,
  MobileCardItem,
  MobileCardActions
} from "@jocom/ui/src/components/table";
import Link from "next/link";

const MyComponent = () => {
  // Define table headers
  const headerNames = ["Name", "Email", "Actions"];
  
  // Define table rows for desktop view
  const rows: Array<TableBodyRow> = [
    {
      id: "row-1",
      key: "row-1",
      items: [
        <TableItemText id="row-1-name" key="row-1-name" text="John Doe" />,
        <TableItemText id="row-1-email" key="row-1-email" text="<EMAIL>" />,
        <TableItemActions id="row-1-actions" key="row-1-actions" editUrl="/users/1" />
      ]
    }
  ];
  
  // Define mobile view rows
  const mobileRows = [
    {
      id: "mobile-row-1",
      key: "mobile-row-1",
      content: (
        <div>
          <MobileCardItem 
            title="Name" 
            value={<div className="font-medium">John Doe</div>} 
          />
          <MobileCardItem 
            title="Email" 
            value="<EMAIL>" 
          />
          <MobileCardActions>
            <Link 
              href="/users/1"
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
              </svg>
            </Link>
          </MobileCardActions>
        </div>
      )
    }
  ];
  
  return (
    <Table 
      headerNames={headerNames} 
      rows={rows} 
      id="users-table" 
      mobileView={{
        enabled: true,
        title: "Users",
        rows: mobileRows
      }}
      breakpoint={768} // Switch to mobile view at 768px
    />
  );
};
```

## Props

### Table Component Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| id | string | Yes | - | Unique identifier for the table |
| headerNames | string[] | Yes | - | Array of column header names |
| rows | TableBodyRow[] | Yes | - | Array of row data for desktop view |
| mobileView | object | No | undefined | Configuration for mobile view |
| mobileView.enabled | boolean | Yes (if mobileView used) | - | Whether to enable mobile view |
| mobileView.title | string | No | undefined | Title to display at the top of mobile view |
| mobileView.rows | MobileTableRow[] | Yes (if mobileView used) | - | Array of row data for mobile view |
| breakpoint | number | No | 640 | Screen width in pixels below which mobile view is shown |

### TableBodyRow Interface

```typescript
interface TableBodyRow {
  id: string;
  items: React.ReactElement[];
  key: string;
}
```

### MobileTableRow Interface

```typescript
interface MobileTableRow {
  id: string;
  content: React.ReactNode;
  key: string;
}
```

## Helper Components

### MobileCardItem

Use this component to create consistent item layouts in mobile view:

```tsx
<MobileCardItem 
  title="Name" 
  value="John Doe" 
/>
```

### MobileCardActions

Use this component to create a consistent action button area in mobile view:

```tsx
<MobileCardActions>
  <Link href="/edit/1">Edit</Link>
  <button onClick={handleDelete}>Delete</button>
</MobileCardActions>
```

## Table Item Components

The package includes several pre-built components for common table cell types:

- `TableItemText`: For displaying simple text
- `TableItemState`: For displaying status with color indicators
- `TableItemNameImage`: For displaying a name with an avatar image
- `TableItemManyOptions`: For displaying multiple tags/options
- `TableItemActions`: For displaying action buttons (edit, delete, etc.)

## Example Implementation

Here's a complete example showing how to implement the Table component with both desktop and mobile views:

```tsx
import { getAllUsers } from "@/actions/user-actions";
import {
  ColorState,
  MobileCardItem,
  MobileCardActions,
  Table,
  TableBodyRow,
  TableItemActions,
  TableItemNameImage,
  TableItemState,
  TableItemText,
} from "@jocom/ui/src/components/table";
import Link from "next/link";

const UsersPage = async () => {
  const users = await getAllUsers();
  
  // Regular table rows
  const rows: Array<TableBodyRow> = [];
  // Mobile view rows
  const mobileRows = [];
  
  const headerNames = ["User", "Email", "Status", "Role", "Actions"];
  
  users.forEach((user, index) => {
    // Regular table row
    const row: TableBodyRow = {
      id: `row-${user.id}`,
      key: `row-${user.id}`,
      items: [
        <TableItemNameImage
          id={`row-${user.id}-name`}
          key={`row-${user.id}-name`}
          name={user.name}
          email={user.email}
          urlImage={user.avatar}
        />,
        <TableItemText
          id={`row-${user.id}-email`}
          key={`row-${user.id}-email`}
          text={user.email}
        />,
        <TableItemState
          id={`row-${user.id}-status`}
          key={`row-${user.id}-status`}
          state={user.status}
          colorState={user.status === "Active" ? ColorState.GREEN : ColorState.RED}
        />,
        <TableItemText
          id={`row-${user.id}-role`}
          key={`row-${user.id}-role`}
          text={user.role}
        />,
        <TableItemActions
          id={`row-${user.id}-actions`}
          key={`row-${user.id}-actions`}
          editUrl={`/users/${user.id}`}
          onDelete={() => handleDelete(user.id)}
        />
      ]
    };
    
    rows.push(row);
    
    // Mobile view row
    mobileRows.push({
      id: `mobile-row-${user.id}`,
      key: `mobile-row-${user.id}`,
      content: (
        <div>
          <div className="flex items-center mb-3">
            <img 
              src={user.avatar} 
              alt={user.name} 
              className="w-10 h-10 rounded-full mr-3" 
            />
            <div>
              <div className="font-medium">{user.name}</div>
              <div className="text-sm text-gray-500">{user.email}</div>
            </div>
          </div>
          
          <MobileCardItem 
            title="Status" 
            value={
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${user.status === "Active" ? "green" : "red"}-100 text-${user.status === "Active" ? "green" : "red"}-800`}>
                {user.status}
              </span>
            } 
          />
          
          <MobileCardItem 
            title="Role" 
            value={user.role} 
          />
          
          <MobileCardActions>
            <Link 
              href={`/users/${user.id}`}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
              </svg>
            </Link>
            <button 
              onClick={() => handleDelete(user.id)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
              </svg>
            </button>
          </MobileCardActions>
        </div>
      )
    });
  });

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Users</h1>
      <Table 
        headerNames={headerNames} 
        rows={rows} 
        id="users-table"
        mobileView={{
          enabled: true,
          title: "Users",
          rows: mobileRows
        }}
        breakpoint={768}
      />
    </div>
  );
};

export default UsersPage;
```