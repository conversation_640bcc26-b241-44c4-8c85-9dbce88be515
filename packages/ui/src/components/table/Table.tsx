"use client";

import React, { useEffect, useState } from "react";
import TableHeader from "./TableHeader";
import { TableProps } from "./TableInterface";

export const Table = ({
  headerNames,
  rows,
  id,
  mobileView,
  breakpoint = 640,
}: TableProps) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < breakpoint);
      };

      // Initial check
      checkMobile();

      // Add event listener for resize
      window.addEventListener("resize", checkMobile);

      // Cleanup
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, [breakpoint]);

  // Render mobile view if enabled and screen is small
  if (mobileView?.enabled && isMobile) {
    return (
      <div
        key={`mobile-table-container-${id}`}
        className="overflow-hidden rounded-lg border border-gray-200 shadow-md m-2 sm:m-5"
      >
        {mobileView.title && (
          <div className="bg-blue-100 dark:bg-gray-800 p-3 font-medium">
            {mobileView.title}
          </div>
        )}
        <div className="divide-y divide-gray-100">
          {mobileView.rows.map((row, index) => (
            <div
              key={row.key}
              className={`p-4 ${
                index % 2 === 0
                  ? "bg-blue-50 dark:bg-slate-600"
                  : "bg-white dark:bg-slate-700"
              }`}
            >
              {row.content}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Regular table view for larger screens
  return (
    <div
      key={`table-container-${id}`}
      className="overflow-hidden rounded-lg border border-gray-200 shadow-md m-2 sm:m-5"
    >
      <div
        className="overflow-x-auto w-full"
        style={{ WebkitOverflowScrolling: "touch" }}
      >
        <table className="min-w-full w-full border-collapse bg-blue-50 dark:bg-slate-600 text-left text-sm">
          <thead className="bg-blue-100 dark:bg-gray-800">
            <TableHeader headerNames={headerNames} />
          </thead>
          <tbody
            key={`table-body-key-${id}`}
            className="divide-y divide-gray-100 border-t border-gray-100"
          >
            {rows.map((row) => (
              <tr
                key={row.id}
                className="hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {row.items}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
