import { NumberOfGridColumns } from "../constants"

export interface TableHeaderProps {
    headerNames:string[]
}

export interface TableBodyRow{
    id:string, items:React.ReactElement[], key:string
}

export interface TableBodyProps{
    rows:Array<TableBodyRow>
}

export interface TableItemNameImageProps {
    id:string,
    key:string,
    name: string, 
    email: string, 
    urlImage: string
}
export interface OptionNameColorState{
    name: string, 
    colorState:ColorState
}

export interface TableItemManyOptionsProps{
    id:string,
    key:string,
    options:OptionNameColorState[],
    numberOfGridColumns: NumberOfGridColumns
}

export interface TableItemTextProps{
    id:string,
    key:string,
    text: string
}

export interface TableItemStateProps{
    id:string,
    key:string,
    state: string, 
    colorState: ColorState
}

export interface TableItemActionsProps{
    id:string,
    key:string,
    editUrl: string|null, 
    onDelete?: ()=>void | null,
    customActions?: React.ReactNode
}

export interface MobileTableRow {
    id: string;
    content: React.ReactNode;
    key: string;
}

export interface TableProps {
    id: string;
    headerNames: string[];
    rows: Array<TableBodyRow>;
    // New mobile-specific props
    mobileView?: {
        enabled: boolean;
        title?: string;
        rows: Array<MobileTableRow>;
    };
    breakpoint?: number; // Screen width in pixels where mobile view kicks in
}

export enum ColorState{
    RED="red",
    GREEN="green",
    YELLOW="yellow",
    BLACK="black",
    BLUE = "blue",
    INDIGO= "indigo",
    VIOLET="violet"

}
