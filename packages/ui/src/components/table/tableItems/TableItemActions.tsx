import Link from "next/link";
import React from "react";
import { TableItemActionsProps } from "../TableInterface";
import { FiEdit } from "react-icons/fi";
import { RiDeleteBin6Line } from "react-icons/ri";

export const TableItemActions = ({
  id,
  editUrl,
  onDelete,
}: TableItemActionsProps) => {
  return (
    <td key={id} className="px-6 py-4">
      <div className="flex justify-end gap-4">
        {editUrl && (
          <Link x-data="{ tooltip: 'Edit' }" href={editUrl}>
            <FiEdit className="h-6 w-6" />
          </Link>
        )}
        {onDelete && (
          <button x-data="{ tooltip: 'Delete' }" onClick={onDelete}>
            <RiDeleteBin6Line className="h-6 w-6" />
          </button>
        )}
      </div>
    </td>
  );
};
