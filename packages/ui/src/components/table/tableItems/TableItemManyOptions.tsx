"use client";

import React, { useEffect, useState } from "react";
import { TableItemManyOptionsProps } from "../TableInterface";

export const TableItemManyOptions = ({
  id,
  options,
  numberOfGridColumns,
}: TableItemManyOptionsProps) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 640);
      };

      // Initial check
      checkMobile();

      // Add event listener for resize
      window.addEventListener("resize", checkMobile);

      // Cleanup
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  return (
    <td key={id} className="px-3 py-3 sm:px-6 sm:py-4">
      <div className={`flex flex-wrap ${numberOfGridColumns}`}>
        {options.length > 3 && isMobile ? (
          <>
            {options.slice(0, 2).map((option) => (
              <span
                key={option.name}
                className={`text-center m-1 items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold text-${option.colorState}-600`}
              >
                {option.name}
              </span>
            ))}
            <span className="text-center m-1 items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold">
              +{options.length - 2}
            </span>
          </>
        ) : (
          options.map((option) => (
            <span
              key={option.name}
              className={`text-center m-1 items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold text-${option.colorState}-600`}
            >
              {option.name}
            </span>
          ))
        )}
      </div>
    </td>
  );
};
