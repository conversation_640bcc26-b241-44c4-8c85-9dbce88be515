import Image from "next/image";
import React from "react";
import { TableItemNameImageProps } from "../TableInterface";

export const TableItemNameImage = ({
  id,
  name,
  email,
  urlImage,
}: TableItemNameImageProps) => {
  return (
    <td
      key={id}
      className="flex gap-2 sm:gap-3 px-3 py-3 sm:px-6 sm:py-4 font-normal whitespace-nowrap"
    >
      <div className="relative h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0">
        <Image
          className="h-full w-full rounded-full object-cover object-center"
          src={urlImage}
          alt={name}
          width={50}
          height={50}
        />
        <span className="absolute right-0 bottom-0 h-2 w-2 rounded-full bg-green-400 ring-3 ring-white"></span>
      </div>
      <div className="text-xs sm:text-sm">
        <div className="font-medium truncate max-w-[100px] sm:max-w-none">
          {name}
        </div>
        <div className="text-gray-400 dark:text-gray-200 truncate max-w-[100px] sm:max-w-none">
          {email}
        </div>
      </div>
    </td>
  );
};
