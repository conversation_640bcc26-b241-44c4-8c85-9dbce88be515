import React from "react";
import { TableItemStateProps } from "../TableInterface";

export const TableItemState = ({
  id,
  state,
  colorState,
}: TableItemStateProps) => {
  return (
    <td key={id} className="px-6 py-4">
      <span
        className={`inline-flex items-center gap-1 rounded-full bg-${colorState}-50 px-2 py-1 text-xs font-semibold text-${colorState}-600`}
      >
        <span
          className={`h-1.5 w-1.5 rounded-full bg-${colorState}-600`}
        ></span>
        {state}
      </span>
    </td>
  );
};
