import React from "react";
import Image from "next/image";

const UserPicker = () => {
  return (
    <div className="flex items-start space-x-4">
      <span> Seleccione usuario</span>
      <div className="relative">
        <Image
          width={50}
          height={50}
          className="w-10 h-10 rounded-full"
          src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
          alt=""
        />
        <span className="top-0 left-7 absolute  w-3.5 h-3.5 bg-purple-400 border-2 border-white dark:border-gray-800 rounded-full"></span>
      </div>
      <div className="relative">
        <Image
          width={50}
          height={50}
          className="w-10 h-10 rounded-sm"
          src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
          alt=""
        />
        <span className="absolute top-0 left-8 transform -translate-y-1/2 w-3.5 h-3.5 bg-blue-400 border-2 border-white dark:border-gray-800 rounded-full"></span>
      </div>
      <div className="relative">
        <Image
          width={50}
          height={50}
          className="w-10 h-10 rounded-full"
          src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
          alt=""
        />
        <span className="bottom-0 left-7 absolute  w-3.5 h-3.5 bg-blue-400 border-2 border-white dark:border-gray-800 rounded-full"></span>
      </div>
      <div className="relative">
        <Image
          width={50}
          height={50}
          className="w-10 h-10 rounded-sm"
          src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
          alt=""
        />
        <span className="absolute bottom-0 left-8 transform translate-y-1/4 w-3.5 h-3.5 bg-purple-400 border-2 border-white dark:border-gray-800 rounded-full"></span>
      </div>
    </div>
  );
};

export default UserPicker;
