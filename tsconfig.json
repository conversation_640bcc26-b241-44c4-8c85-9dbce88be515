{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "baseUrl": ".",
    "paths": {
      "@jocom/ui": ["packages/ui/src"],
      "@jocom/types": ["packages/types/src"],
      "@jocom/config": ["packages/config/src"],
      "@/*": ["apps/*/src"]
    },
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "types": ["node"],
    "allowJs": false, // Set to true only if you have .js files
    "typeRoots": [
      "./node_modules/@types",
      "./packages/types/src"
    ]
  },
  "exclude": [
    "node_modules",
    "**/node_modules",
    "**/.next",
    "**/dist"
  ],
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.js",
    "**/*.jsx",
    "next-env.d.ts"
  ]
}